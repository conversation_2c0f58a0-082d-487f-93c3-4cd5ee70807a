import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../lib/screens/settings_screen_real.dart';
import '../lib/services/settings_service.dart';
import '../lib/models/settings_model.dart';

void main() {
  group('Settings Screen Tests', () {
    late SettingsService settingsService;

    setUp(() {
      settingsService = SettingsService();
    });

    testWidgets('Settings screen loads correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const SettingsScreen(),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Check if the settings screen is displayed
      expect(find.text('Settings'), findsOneWidget);
      expect(find.byType(TabBar), findsOneWidget);
      expect(find.byType(TabBarView), findsOneWidget);
    });

    testWidgets('Theme mode switch works', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const SettingsScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the theme dropdown
      final themeDropdown = find.byType(DropdownButton<AppThemeMode>);
      expect(themeDropdown, findsOneWidget);

      await tester.tap(themeDropdown);
      await tester.pumpAndSettle();

      // Check if dropdown items are displayed
      expect(find.text('فاتح'), findsOneWidget);
      expect(find.text('داكن'), findsOneWidget);
    });

    testWidgets('Switch tiles work correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const SettingsScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find switch tiles
      final switchTiles = find.byType(SwitchListTile);
      expect(switchTiles, findsWidgets);

      // Test first switch tile
      if (switchTiles.evaluate().isNotEmpty) {
        await tester.tap(switchTiles.first);
        await tester.pumpAndSettle();
        // The switch should toggle (no exception should be thrown)
      }
    });

    testWidgets('Slider tiles work correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const SettingsScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find slider widgets
      final sliders = find.byType(Slider);
      expect(sliders, findsWidgets);

      // Test first slider
      if (sliders.evaluate().isNotEmpty) {
        final slider = tester.widget<Slider>(sliders.first);
        expect(slider.onChanged, isNotNull);
      }
    });

    testWidgets('Menu actions work', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const SettingsScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the menu button
      final menuButton = find.byType(PopupMenuButton<String>);
      expect(menuButton, findsOneWidget);

      await tester.tap(menuButton);
      await tester.pumpAndSettle();

      // Check if menu items are displayed
      expect(find.text('Export Settings'), findsOneWidget);
      expect(find.text('Import Settings'), findsOneWidget);
      expect(find.text('Reset Settings'), findsOneWidget);
    });

    test('SettingsService methods work correctly', () async {
      // Test theme mode setting
      await settingsService.setThemeMode(AppThemeMode.dark);
      expect(settingsService.settings.themeMode, AppThemeMode.dark);

      // Test language setting
      await settingsService.setLanguage(Language.english);
      expect(settingsService.settings.language, Language.english);

      // Test boolean settings
      await settingsService.setEnableAnimations(false);
      expect(settingsService.settings.enableAnimations, false);

      // Test numeric settings
      await settingsService.setFontSize(18.0);
      expect(settingsService.settings.fontSize, 18.0);

      await settingsService.setDefaultQuestionCount(15);
      expect(settingsService.settings.defaultQuestionCount, 15);

      // Test reset to defaults
      await settingsService.resetToDefaults();
      expect(settingsService.settings.themeMode, AppThemeMode.system);
      expect(settingsService.settings.language, Language.arabic);
    });

    test('Settings export and import work', () async {
      // Set some custom settings
      await settingsService.setThemeMode(AppThemeMode.dark);
      await settingsService.setLanguage(Language.english);
      await settingsService.setFontSize(20.0);

      // Export settings
      final exportedSettings = settingsService.exportSettings();
      expect(exportedSettings, isNotEmpty);

      // Reset to defaults
      await settingsService.resetToDefaults();
      expect(settingsService.settings.themeMode, AppThemeMode.system);

      // Import settings
      final importSuccess = await settingsService.importSettings(exportedSettings);
      expect(importSuccess, true);
      expect(settingsService.settings.themeMode, AppThemeMode.dark);
      expect(settingsService.settings.language, Language.english);
      expect(settingsService.settings.fontSize, 20.0);
    });
  });
}
