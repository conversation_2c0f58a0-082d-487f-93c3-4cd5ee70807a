import 'package:flutter_test/flutter_test.dart';
import 'package:quepdf/services/storage_service.dart';

void main() {
  group('StorageService Tests', () {
    test('Storage service should handle initialization gracefully', () async {
      final storageService = StorageService();
      
      // This should not throw an exception even if platform channels aren't available
      expect(() async => await storageService.initialize(), returnsNormally);
    });
    
    test('Storage service should be singleton', () {
      final storage1 = StorageService();
      final storage2 = StorageService();
      
      expect(storage1, equals(storage2));
    });
  });
}
