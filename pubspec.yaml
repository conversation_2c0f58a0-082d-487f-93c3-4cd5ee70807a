name: quepdf
description: "Professional PDF to Quiz converter with AI-powered question generation"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Navigation
  cupertino_icons: ^1.0.8
  go_router: ^14.2.7
  flutter_riverpod: ^2.5.1

  # PDF Processing
  syncfusion_flutter_pdf: ^27.1.48
  file_picker: ^8.1.2

  # AI Integration
  google_generative_ai: ^0.4.6

  # Storage & Persistence
  sqflite: ^2.3.3+1
  shared_preferences: ^2.3.2
  path_provider: ^2.1.4

  # Networking
  http: ^1.2.2
  dio: ^5.7.0

  # UI Components
  flutter_spinkit: ^5.2.1
  lottie: ^3.1.2
  cached_network_image: ^3.4.1

  # Utilities
  intl: ^0.19.0
  uuid: ^4.5.1
  permission_handler: ^11.3.1
  path: ^1.9.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/lottie/
    - assets/icons/

  # fonts:
  #   - family: Cairo
  #     fonts:
  #       - asset: assets/fonts/Cairo-Regular.ttf
  #       - asset: assets/fonts/Cairo-Bold.ttf
  #         weight: 700
