import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/settings_model.dart';

class SettingsService extends ChangeNotifier {
  static final SettingsService _instance = SettingsService._internal();
  factory SettingsService() => _instance;
  SettingsService._internal();

  static const String _settingsKey = 'app_settings';
  
  SharedPreferences? _prefs;
  AppSettings _settings = const AppSettings();
  bool _isInitialized = false;

  AppSettings get settings => _settings;
  bool get isInitialized => _isInitialized;

  /// Initialize the settings service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Settings initialization failed: $e');
      // Use default settings if initialization fails
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// Load settings from storage
  Future<void> _loadSettings() async {
    try {
      final settingsJson = _prefs?.getString(_settingsKey);
      if (settingsJson != null) {
        final settingsMap = json.decode(settingsJson) as Map<String, dynamic>;
        _settings = AppSettings.fromMap(settingsMap);
      }
    } catch (e) {
      debugPrint('Failed to load settings: $e');
      // Keep default settings if loading fails
    }
  }

  /// Save settings to storage
  Future<void> _saveSettings() async {
    try {
      final settingsJson = _settings.toJson();
      await _prefs?.setString(_settingsKey, settingsJson);
    } catch (e) {
      debugPrint('Failed to save settings: $e');
    }
  }

  /// Update settings and save
  Future<void> updateSettings(AppSettings newSettings) async {
    _settings = newSettings;
    await _saveSettings();
    notifyListeners();
  }

  // Appearance Settings
  Future<void> setThemeMode(AppThemeMode themeMode) async {
    await updateSettings(_settings.copyWith(themeMode: themeMode));
  }

  Future<void> setLanguage(Language language) async {
    await updateSettings(_settings.copyWith(language: language));
  }

  Future<void> setEnableAnimations(bool enable) async {
    await updateSettings(_settings.copyWith(enableAnimations: enable));
  }

  Future<void> setFontSize(double fontSize) async {
    await updateSettings(_settings.copyWith(fontSize: fontSize));
  }

  Future<void> setEnableRTL(bool enable) async {
    await updateSettings(_settings.copyWith(enableRTL: enable));
  }

  // Quiz Settings
  Future<void> setDefaultQuestionCount(int count) async {
    await updateSettings(_settings.copyWith(defaultQuestionCount: count));
  }

  Future<void> setDefaultTimeLimit(int timeLimit) async {
    await updateSettings(_settings.copyWith(defaultTimeLimit: timeLimit));
  }

  Future<void> setDefaultDifficulty(QuizDifficulty difficulty) async {
    await updateSettings(_settings.copyWith(defaultDifficulty: difficulty));
  }

  Future<void> setAutoSubmitAnswers(bool enable) async {
    await updateSettings(_settings.copyWith(autoSubmitAnswers: enable));
  }

  Future<void> setShowCorrectAnswers(bool enable) async {
    await updateSettings(_settings.copyWith(showCorrectAnswers: enable));
  }

  Future<void> setEnableHints(bool enable) async {
    await updateSettings(_settings.copyWith(enableHints: enable));
  }

  Future<void> setShuffleQuestions(bool enable) async {
    await updateSettings(_settings.copyWith(shuffleQuestions: enable));
  }

  Future<void> setShuffleAnswers(bool enable) async {
    await updateSettings(_settings.copyWith(shuffleAnswers: enable));
  }

  // PDF Settings
  Future<void> setMaxPdfSizeMB(int sizeMB) async {
    await updateSettings(_settings.copyWith(maxPdfSizeMB: sizeMB));
  }

  Future<void> setExtractImages(bool enable) async {
    await updateSettings(_settings.copyWith(extractImages: enable));
  }

  Future<void> setProcessFormulas(bool enable) async {
    await updateSettings(_settings.copyWith(processFormulas: enable));
  }

  Future<void> setEnhanceTextQuality(bool enable) async {
    await updateSettings(_settings.copyWith(enhanceTextQuality: enable));
  }

  // AI Settings
  Future<void> setGeminiApiKey(String apiKey) async {
    await updateSettings(_settings.copyWith(geminiApiKey: apiKey));
  }

  Future<void> setAiTemperature(double temperature) async {
    await updateSettings(_settings.copyWith(aiTemperature: temperature));
  }

  Future<void> setMaxTokens(int maxTokens) async {
    await updateSettings(_settings.copyWith(maxTokens: maxTokens));
  }

  Future<void> setEnableContextualQuestions(bool enable) async {
    await updateSettings(_settings.copyWith(enableContextualQuestions: enable));
  }

  Future<void> setGenerateExplanations(bool enable) async {
    await updateSettings(_settings.copyWith(generateExplanations: enable));
  }

  // Notification Settings
  Future<void> setEnableNotifications(bool enable) async {
    await updateSettings(_settings.copyWith(enableNotifications: enable));
  }

  Future<void> setReminderFrequency(NotificationFrequency frequency) async {
    await updateSettings(_settings.copyWith(reminderFrequency: frequency));
  }

  Future<void> setSoundEnabled(bool enable) async {
    await updateSettings(_settings.copyWith(soundEnabled: enable));
  }

  Future<void> setVibrationEnabled(bool enable) async {
    await updateSettings(_settings.copyWith(vibrationEnabled: enable));
  }

  // Privacy Settings
  Future<void> setSaveQuizHistory(bool enable) async {
    await updateSettings(_settings.copyWith(saveQuizHistory: enable));
  }

  Future<void> setShareAnalytics(bool enable) async {
    await updateSettings(_settings.copyWith(shareAnalytics: enable));
  }

  Future<void> setEnableCrashReporting(bool enable) async {
    await updateSettings(_settings.copyWith(enableCrashReporting: enable));
  }

  Future<void> setAutoBackup(bool enable) async {
    await updateSettings(_settings.copyWith(autoBackup: enable));
  }

  // Performance Settings
  Future<void> setEnableCaching(bool enable) async {
    await updateSettings(_settings.copyWith(enableCaching: enable));
  }

  Future<void> setCacheSize(int sizeMB) async {
    await updateSettings(_settings.copyWith(cacheSize: sizeMB));
  }

  Future<void> setPreloadContent(bool enable) async {
    await updateSettings(_settings.copyWith(preloadContent: enable));
  }

  Future<void> setOptimizeImages(bool enable) async {
    await updateSettings(_settings.copyWith(optimizeImages: enable));
  }

  // Accessibility Settings
  Future<void> setEnableScreenReader(bool enable) async {
    await updateSettings(_settings.copyWith(enableScreenReader: enable));
  }

  Future<void> setHighContrast(bool enable) async {
    await updateSettings(_settings.copyWith(highContrast: enable));
  }

  Future<void> setLargeText(bool enable) async {
    await updateSettings(_settings.copyWith(largeText: enable));
  }

  Future<void> setReduceMotion(bool enable) async {
    await updateSettings(_settings.copyWith(reduceMotion: enable));
  }

  /// Reset all settings to defaults
  Future<void> resetToDefaults() async {
    await updateSettings(const AppSettings());
  }

  /// Export settings as JSON string
  String exportSettings() {
    return _settings.toJson();
  }

  /// Import settings from JSON string
  Future<bool> importSettings(String settingsJson) async {
    try {
      final settingsMap = json.decode(settingsJson) as Map<String, dynamic>;
      final newSettings = AppSettings.fromMap(settingsMap);
      await updateSettings(newSettings);
      return true;
    } catch (e) {
      debugPrint('Failed to import settings: $e');
      return false;
    }
  }

  /// Clear all settings data
  Future<void> clearAllData() async {
    try {
      await _prefs?.remove(_settingsKey);
      _settings = const AppSettings();
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to clear settings data: $e');
    }
  }

  /// Get settings summary for display
  Map<String, String> getSettingsSummary() {
    return {
      'Theme': _settings.themeMode.name,
      'Language': _settings.language.name,
      'Default Questions': _settings.defaultQuestionCount.toString(),
      'Time Limit': _settings.defaultTimeLimit == 0 ? 'No limit' : '${_settings.defaultTimeLimit} min',
      'Difficulty': _settings.defaultDifficulty.name,
      'Auto Submit': _settings.autoSubmitAnswers ? 'Enabled' : 'Disabled',
      'Notifications': _settings.enableNotifications ? 'Enabled' : 'Disabled',
      'Cache Size': '${_settings.cacheSize} MB',
    };
  }
}
