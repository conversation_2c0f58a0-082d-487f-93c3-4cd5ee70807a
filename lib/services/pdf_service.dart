import 'dart:io';
import 'dart:typed_data';
import 'package:file_picker/file_picker.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import '../config/app_config.dart';

class PdfExtractionResult {
  final String text;
  final String fileName;
  final String filePath;
  final int pageCount;
  final double fileSizeKB;

  PdfExtractionResult({
    required this.text,
    required this.fileName,
    required this.filePath,
    required this.pageCount,
    required this.fileSizeKB,
  });
}

class PdfService {
  static final PdfService _instance = PdfService._internal();
  factory PdfService() => _instance;
  PdfService._internal();

  /// Pick a PDF file from device storage
  Future<File?> pickPdfFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        
        // Validate file size
        final fileSizeBytes = await file.length();
        final fileSizeMB = fileSizeBytes / (1024 * 1024);
        
        if (fileSizeMB > AppConfig.maxPdfSizeMB) {
          throw PdfServiceException(
            'File size (${fileSizeMB.toStringAsFixed(1)} MB) exceeds maximum allowed size (${AppConfig.maxPdfSizeMB} MB)',
          );
        }

        return file;
      }
      return null;
    } catch (e) {
      if (e is PdfServiceException) rethrow;
      throw PdfServiceException('Failed to pick PDF file: ${e.toString()}');
    }
  }

  /// Extract text content from PDF file
  Future<PdfExtractionResult> extractTextFromPdf(File pdfFile) async {
    try {
      // Read PDF file as bytes
      final Uint8List bytes = await pdfFile.readAsBytes();
      
      // Load PDF document
      final PdfDocument document = PdfDocument(inputBytes: bytes);
      
      // Extract text from all pages
      final StringBuffer textBuffer = StringBuffer();
      final PdfTextExtractor extractor = PdfTextExtractor(document);
      
      for (int i = 0; i < document.pages.count; i++) {
        final String? pageText = extractor.extractText(startPageIndex: i, endPageIndex: i);
        if (pageText != null && pageText.isNotEmpty) {
          textBuffer.writeln(pageText);
          textBuffer.writeln(); // Add spacing between pages
        }
      }
      
      final String extractedText = textBuffer.toString().trim();
      
      // Validate extracted text
      if (extractedText.isEmpty) {
        throw PdfServiceException('No text content found in the PDF file');
      }
      
      if (extractedText.length < 100) {
        throw PdfServiceException('PDF content is too short to generate meaningful questions');
      }
      
      // Get file information
      final fileSizeBytes = await pdfFile.length();
      final fileSizeKB = fileSizeBytes / 1024;
      
      // Clean up
      document.dispose();
      
      return PdfExtractionResult(
        text: extractedText,
        fileName: pdfFile.path.split('/').last,
        filePath: pdfFile.path,
        pageCount: document.pages.count,
        fileSizeKB: fileSizeKB,
      );
      
    } catch (e) {
      if (e is PdfServiceException) rethrow;
      throw PdfServiceException('Failed to extract text from PDF: ${e.toString()}');
    }
  }

  /// Validate PDF file
  Future<bool> validatePdfFile(File pdfFile) async {
    try {
      // Check file extension
      if (!pdfFile.path.toLowerCase().endsWith('.pdf')) {
        return false;
      }
      
      // Check file size
      final fileSizeBytes = await pdfFile.length();
      final fileSizeMB = fileSizeBytes / (1024 * 1024);
      
      if (fileSizeMB > AppConfig.maxPdfSizeMB) {
        return false;
      }
      
      // Try to load PDF document to validate format
      final Uint8List bytes = await pdfFile.readAsBytes();
      final PdfDocument document = PdfDocument(inputBytes: bytes);
      
      // Check if document has pages
      final bool isValid = document.pages.count > 0;
      
      // Clean up
      document.dispose();
      
      return isValid;
      
    } catch (e) {
      return false;
    }
  }

  /// Get PDF file information without extracting text
  Future<Map<String, dynamic>> getPdfInfo(File pdfFile) async {
    try {
      final Uint8List bytes = await pdfFile.readAsBytes();
      final PdfDocument document = PdfDocument(inputBytes: bytes);
      
      final fileSizeBytes = await pdfFile.length();
      final fileSizeKB = fileSizeBytes / 1024;
      final fileSizeMB = fileSizeBytes / (1024 * 1024);
      
      final info = {
        'fileName': pdfFile.path.split('/').last,
        'filePath': pdfFile.path,
        'pageCount': document.pages.count,
        'fileSizeBytes': fileSizeBytes,
        'fileSizeKB': fileSizeKB,
        'fileSizeMB': fileSizeMB,
        'title': document.documentInformation.title,
        'author': document.documentInformation.author,
        'subject': document.documentInformation.subject,
        'creator': document.documentInformation.creator,
        'creationDate': document.documentInformation.creationDate.toString(),
        'modificationDate': document.documentInformation.modificationDate.toString(),
      };
      
      // Clean up
      document.dispose();
      
      return info;
      
    } catch (e) {
      throw PdfServiceException('Failed to get PDF information: ${e.toString()}');
    }
  }

  /// Clean and preprocess extracted text for better AI processing
  String preprocessText(String text) {
    // Remove excessive whitespace
    text = text.replaceAll(RegExp(r'\s+'), ' ');
    
    // Remove page numbers and headers/footers (basic patterns)
    text = text.replaceAll(RegExp(r'\n\d+\n'), '\n');
    text = text.replaceAll(RegExp(r'\nPage \d+\n'), '\n');
    
    // Remove URLs
    text = text.replaceAll(RegExp(r'https?://[^\s]+'), '');

    // Remove email addresses
    text = text.replaceAll(RegExp(r'\S+@\S+\.\S+'), '');

    // Clean up special characters but keep essential punctuation
    text = text.replaceAll(RegExp(r'[^\w\s\.\,\!\?\;\:\-\(\)\[\]\{\}]+'), ' ');
    
    // Remove excessive line breaks
    text = text.replaceAll(RegExp(r'\n{3,}'), '\n\n');
    
    // Trim and return
    return text.trim();
  }
}

class PdfServiceException implements Exception {
  final String message;
  
  PdfServiceException(this.message);
  
  @override
  String toString() => 'PdfServiceException: $message';
}
