import 'dart:convert';
import 'dart:math';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../models/question_model.dart';
import '../config/app_config.dart';

class QuizGenerationRequest {
  final String pdfContent;
  final int questionCount;
  final QuestionDifficulty? preferredDifficulty;
  final String? category;
  final String? language;

  QuizGenerationRequest({
    required this.pdfContent,
    required this.questionCount,
    this.preferredDifficulty,
    this.category,
    this.language = 'en',
  });
}

class QuizGenerationResult {
  final List<Question> questions;
  final String? feedback;
  final Map<String, dynamic>? metadata;

  QuizGenerationResult({
    required this.questions,
    this.feedback,
    this.metadata,
  });
}

class GeminiService {
  static final GeminiService _instance = GeminiService._internal();
  factory GeminiService() => _instance;
  GeminiService._internal();

  GenerativeModel? _model;
  
  /// Initialize the Gemini service with API key
  void initialize(String apiKey) {
    if (apiKey.isEmpty) {
      throw GeminiServiceException('Gemini API key is required');
    }
    
    _model = GenerativeModel(
      model: AppConfig.geminiModel,
      apiKey: apiKey,
      generationConfig: GenerationConfig(
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 8192,
      ),
      safetySettings: [
        SafetySetting(HarmCategory.harassment, HarmBlockThreshold.medium),
        SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.medium),
        SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.medium),
        SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.medium),
      ],
    );
  }

  /// Generate quiz questions from PDF content
  Future<QuizGenerationResult> generateQuiz(QuizGenerationRequest request) async {
    if (_model == null) {
      throw GeminiServiceException('Gemini service not initialized. Please provide API key.');
    }

    try {
      // Validate input
      if (request.pdfContent.trim().isEmpty) {
        throw GeminiServiceException('PDF content is empty');
      }

      if (request.questionCount < AppConfig.minQuestionsPerQuiz || 
          request.questionCount > AppConfig.maxQuestionsPerQuiz) {
        throw GeminiServiceException(
          'Question count must be between ${AppConfig.minQuestionsPerQuiz} and ${AppConfig.maxQuestionsPerQuiz}'
        );
      }

      // Prepare the prompt
      final prompt = _buildQuizGenerationPrompt(request);
      
      // Generate content
      final response = await _model!.generateContent([Content.text(prompt)]);
      
      if (response.text == null || response.text!.isEmpty) {
        throw GeminiServiceException('Empty response from Gemini API');
      }

      // Parse the response
      final questions = _parseQuizResponse(response.text!);
      
      if (questions.isEmpty) {
        throw GeminiServiceException('No valid questions generated');
      }

      return QuizGenerationResult(
        questions: questions,
        feedback: _generateFeedback(questions),
        metadata: {
          'generatedAt': DateTime.now().toIso8601String(),
          'requestedCount': request.questionCount,
          'actualCount': questions.length,
          'contentLength': request.pdfContent.length,
          'language': request.language,
        },
      );

    } catch (e) {
      if (e is GeminiServiceException) rethrow;
      throw GeminiServiceException('Failed to generate quiz: ${e.toString()}');
    }
  }

  /// Generate feedback for quiz results
  Future<String> generateQuizFeedback({
    required List<Question> questions,
    required List<bool> answers,
    required double scorePercentage,
    required Duration totalTime,
  }) async {
    if (_model == null) {
      throw GeminiServiceException('Gemini service not initialized');
    }

    try {
      final prompt = _buildFeedbackPrompt(questions, answers, scorePercentage, totalTime);
      
      final response = await _model!.generateContent([Content.text(prompt)]);
      
      return response.text ?? 'Great job completing the quiz!';
      
    } catch (e) {
      // Return default feedback if AI generation fails
      return _generateDefaultFeedback(scorePercentage);
    }
  }

  /// Build the quiz generation prompt
  String _buildQuizGenerationPrompt(QuizGenerationRequest request) {
    final difficultyInstruction = request.preferredDifficulty != null
        ? 'Focus on ${request.preferredDifficulty!.name} difficulty questions.'
        : 'Mix easy, medium, and hard difficulty questions.';

    final languageInstruction = request.language == 'ar'
        ? 'Generate questions in Arabic language with proper RTL formatting.'
        : 'Generate questions in English language.';

    final categoryInstruction = request.category != null
        ? 'Focus on the ${request.category} category/topic.'
        : '';

    return '''
You are an expert quiz generator. Create exactly ${request.questionCount} high-quality multiple-choice questions based on the provided content.

Requirements:
1. Each question must have exactly 4 options (A, B, C, D)
2. Only one option should be correct
3. Provide clear explanations for correct answers
4. $difficultyInstruction
5. $languageInstruction
6. $categoryInstruction
7. Questions should test understanding, not just memorization
8. Avoid ambiguous or trick questions
9. Ensure options are plausible and well-distributed

Content to analyze:
${request.pdfContent.length > 4000 ? request.pdfContent.substring(0, 4000) + '...' : request.pdfContent}

Return ONLY a valid JSON object with this exact structure:
{
  "questions": [
    {
      "text": "Clear, specific question text",
      "options": ["Option A", "Option B", "Option C", "Option D"],
      "correctAnswerIndex": 0,
      "explanation": "Brief explanation of why this answer is correct",
      "difficulty": "easy",
      "category": "optional category name"
    }
  ]
}

Important: Return only the JSON object, no additional text or formatting.
''';
  }

  /// Parse the AI response and extract questions
  List<Question> _parseQuizResponse(String response) {
    try {
      // Clean the response
      String cleanResponse = response.trim();
      
      // Remove markdown code blocks if present
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.substring(7);
      }
      if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.substring(3);
      }
      if (cleanResponse.endsWith('```')) {
        cleanResponse = cleanResponse.substring(0, cleanResponse.length - 3);
      }
      
      // Parse JSON
      final Map<String, dynamic> jsonResponse = json.decode(cleanResponse);
      final List<dynamic> questionsJson = jsonResponse['questions'] ?? [];
      
      final List<Question> questions = [];
      
      for (int i = 0; i < questionsJson.length; i++) {
        try {
          final questionData = questionsJson[i] as Map<String, dynamic>;
          
          // Validate required fields
          if (!questionData.containsKey('text') || 
              !questionData.containsKey('options') || 
              !questionData.containsKey('correctAnswerIndex')) {
            continue;
          }
          
          final options = List<String>.from(questionData['options'] ?? []);
          if (options.length != 4) continue;
          
          final correctIndex = questionData['correctAnswerIndex'] as int;
          if (correctIndex < 0 || correctIndex >= options.length) continue;
          
          final question = Question(
            id: _generateQuestionId(),
            text: questionData['text'] as String,
            type: QuestionType.multipleChoice,
            options: options,
            correctAnswerIndex: correctIndex,
            explanation: questionData['explanation'] as String? ?? '',
            difficulty: _parseDifficulty(questionData['difficulty'] as String?),
            category: questionData['category'] as String?,
            createdAt: DateTime.now(),
          );
          
          questions.add(question);
        } catch (e) {
          // Skip invalid questions
          continue;
        }
      }
      
      return questions;
      
    } catch (e) {
      throw GeminiServiceException('Failed to parse quiz response: ${e.toString()}');
    }
  }

  /// Generate unique question ID
  String _generateQuestionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(10000);
    return 'q_${timestamp}_$random';
  }

  /// Parse difficulty from string
  QuestionDifficulty _parseDifficulty(String? difficulty) {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return QuestionDifficulty.easy;
      case 'hard':
        return QuestionDifficulty.hard;
      default:
        return QuestionDifficulty.medium;
    }
  }

  /// Build feedback generation prompt
  String _buildFeedbackPrompt(
    List<Question> questions,
    List<bool> answers,
    double scorePercentage,
    Duration totalTime,
  ) {
    final correctCount = answers.where((a) => a).length;
    final totalCount = answers.length;
    
    return '''
Generate personalized feedback for a quiz performance:

Quiz Results:
- Score: $correctCount/$totalCount (${scorePercentage.toStringAsFixed(1)}%)
- Time taken: ${totalTime.inMinutes} minutes ${totalTime.inSeconds % 60} seconds
- Performance level: ${_getPerformanceLevel(scorePercentage)}

Provide encouraging, constructive feedback that:
1. Acknowledges the effort
2. Highlights strengths
3. Suggests areas for improvement (if applicable)
4. Motivates continued learning
5. Keep it concise (2-3 sentences)

Make the feedback positive and supportive regardless of the score.
''';
  }

  /// Generate default feedback when AI fails
  String _generateDefaultFeedback(double scorePercentage) {
    if (scorePercentage >= 90) {
      return 'Excellent work! You demonstrated outstanding understanding of the material.';
    } else if (scorePercentage >= 75) {
      return 'Great job! You have a solid grasp of the concepts covered in this quiz.';
    } else if (scorePercentage >= 60) {
      return 'Good effort! Review the explanations to strengthen your understanding.';
    } else {
      return 'Keep practicing! Use the explanations to learn and improve for next time.';
    }
  }

  /// Generate feedback for quiz generation
  String? _generateFeedback(List<Question> questions) {
    final difficultyDistribution = <QuestionDifficulty, int>{};
    for (final question in questions) {
      difficultyDistribution[question.difficulty] = 
          (difficultyDistribution[question.difficulty] ?? 0) + 1;
    }
    
    return 'Generated ${questions.length} questions with varied difficulty levels.';
  }

  /// Get performance level description
  String _getPerformanceLevel(double scorePercentage) {
    if (scorePercentage >= 90) return 'Excellent';
    if (scorePercentage >= 80) return 'Very Good';
    if (scorePercentage >= 70) return 'Good';
    if (scorePercentage >= 60) return 'Satisfactory';
    return 'Needs Improvement';
  }
}

class GeminiServiceException implements Exception {
  final String message;
  
  GeminiServiceException(this.message);
  
  @override
  String toString() => 'GeminiServiceException: $message';
}
