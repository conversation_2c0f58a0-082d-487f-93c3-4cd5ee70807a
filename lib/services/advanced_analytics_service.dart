import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import '../models/analytics_models.dart';
import '../models/quiz_result_model.dart';
import '../models/quiz_model.dart';
import 'storage_service.dart';

class AdvancedAnalyticsService extends ChangeNotifier {
  static final AdvancedAnalyticsService _instance = AdvancedAnalyticsService._internal();
  factory AdvancedAnalyticsService() => _instance;
  AdvancedAnalyticsService._internal();

  final StorageService _storageService = StorageService();
  
  // Cache for analytics data
  KnowledgeMap? _cachedKnowledgeMap;
  LearningCurve? _cachedLearningCurve;
  List<ErrorPattern>? _cachedErrorPatterns;
  List<PerformancePrediction>? _cachedPredictions;

  /// Initialize the analytics service
  Future<void> initialize() async {
    await _storageService.initialize();
    await _loadCachedData();
  }

  /// Load cached analytics data
  Future<void> _loadCachedData() async {
    try {
      // Load from storage if available
      // For now, we'll generate from quiz results
      await refreshAnalytics();
    } catch (e) {
      debugPrint('Error loading cached analytics data: $e');
    }
  }

  /// Refresh all analytics data
  Future<void> refreshAnalytics() async {
    try {
      final results = await _storageService.getAllQuizResults();
      final quizzes = await _storageService.getAllQuizzes();
      
      await Future.wait([
        _generateKnowledgeMap(results, quizzes),
        _generateLearningCurve(results),
        _generateErrorPatterns(results, quizzes),
        _generatePerformancePredictions(results),
      ]);
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error refreshing analytics: $e');
    }
  }

  /// Generate Knowledge Map from quiz results
  Future<void> _generateKnowledgeMap(List<QuizResult> results, List<Quiz> quizzes) async {
    final Map<String, TopicKnowledge> topics = {};
    final Map<String, List<String>> topicRelations = {};

    // Group results by topic
    final Map<String, List<QuizResult>> topicResults = {};
    for (final result in results) {
      final quiz = quizzes.firstWhere(
        (q) => q.id == result.quizId,
        orElse: () => Quiz(
          id: result.quizId,
          title: result.quizTitle,
          description: '',
          questions: [],
          createdAt: DateTime.now(),
          sourceFileName: 'unknown',
          status: QuizStatus.completed,
        ),
      );
      
      final topicName = _extractTopicFromTitle(quiz.title);
      topicResults.putIfAbsent(topicName, () => []).add(result);
    }

    // Calculate knowledge for each topic
    for (final entry in topicResults.entries) {
      final topicName = entry.key;
      final topicResultsList = entry.value;
      
      final totalQuestions = topicResultsList.fold<int>(
        0, (sum, result) => sum + result.questionResults.length,
      );
      
      final correctAnswers = topicResultsList.fold<int>(
        0, (sum, result) => sum + result.questionResults.where((q) => q.isCorrect).length,
      );
      
      final averageTime = topicResultsList.fold<double>(
        0.0, (sum, result) => sum + result.questionResults.fold<double>(
          0.0, (qSum, q) => qSum + q.timeSpentSeconds,
        ),
      ) / math.max(totalQuestions, 1);
      
      final masteryLevel = totalQuestions > 0 ? correctAnswers / totalQuestions : 0.0;
      
      final level = _calculateKnowledgeLevel(masteryLevel);
      
      topics[topicName] = TopicKnowledge(
        topicName: topicName,
        masteryLevel: masteryLevel,
        totalQuestions: totalQuestions,
        correctAnswers: correctAnswers,
        averageTime: averageTime,
        relatedQuizzes: topicResultsList.map((r) => r.quizId).toList(),
        lastPracticed: topicResultsList.map((r) => r.completedAt).where((d) => d != null).fold<DateTime?>(
          null, (latest, date) => latest == null || date!.isAfter(latest) ? date : latest,
        ) ?? DateTime.now(),
        level: level,
      );
    }

    // Generate topic relations (simplified)
    for (final topic in topics.keys) {
      topicRelations[topic] = topics.keys.where((t) => t != topic).take(3).toList();
    }

    _cachedKnowledgeMap = KnowledgeMap(
      id: 'km_${DateTime.now().millisecondsSinceEpoch}',
      userId: 'current_user',
      topics: topics,
      lastUpdated: DateTime.now(),
      topicRelations: topicRelations,
    );
  }

  /// Generate Learning Curve from quiz results
  Future<void> _generateLearningCurve(List<QuizResult> results) async {
    final List<LearningPoint> dataPoints = [];
    
    // Sort results by completion date
    results.sort((a, b) => (a.completedAt ?? DateTime.now()).compareTo(b.completedAt ?? DateTime.now()));
    
    for (final result in results) {
      if (result.completedAt == null) continue;
      
      final totalQuestions = result.questionResults.length;
      final correctAnswers = result.questionResults.where((q) => q.isCorrect).length;
      final totalTime = result.questionResults.fold<int>(0, (sum, q) => sum + q.timeSpentSeconds);
      
      final accuracy = totalQuestions > 0 ? correctAnswers / totalQuestions : 0.0;
      final speed = totalTime > 0 ? (totalQuestions * 60) / totalTime : 0.0; // questions per minute
      
      dataPoints.add(LearningPoint(
        timestamp: result.completedAt!,
        score: result.scorePercentage / 100,
        accuracy: accuracy,
        speed: speed,
        quizId: result.quizId,
        topicName: _extractTopicFromTitle(result.quizTitle),
        isImportantEvent: _isImportantEvent(result),
        eventDescription: _getEventDescription(result),
      ));
    }

    final trend = _calculateLearningTrend(dataPoints);

    _cachedLearningCurve = LearningCurve(
      id: 'lc_${DateTime.now().millisecondsSinceEpoch}',
      userId: 'current_user',
      dataPoints: dataPoints,
      startDate: dataPoints.isNotEmpty ? dataPoints.first.timestamp : DateTime.now(),
      endDate: dataPoints.isNotEmpty ? dataPoints.last.timestamp : DateTime.now(),
      trend: trend,
    );
  }

  /// Generate Error Patterns from quiz results
  Future<void> _generateErrorPatterns(List<QuizResult> results, List<Quiz> quizzes) async {
    final Map<String, Map<ErrorType, int>> topicErrors = {};
    final Map<String, List<String>> topicMistakes = {};

    for (final result in results) {
      final quiz = quizzes.firstWhere(
        (q) => q.id == result.quizId,
        orElse: () => Quiz(
          id: result.quizId,
          title: result.quizTitle,
          description: '',
          questions: [],
          createdAt: DateTime.now(),
          sourceFileName: 'unknown',
          status: QuizStatus.completed,
        ),
      );
      
      final topicName = _extractTopicFromTitle(quiz.title);
      
      for (int i = 0; i < result.questionResults.length; i++) {
        final questionResult = result.questionResults[i];
        if (!questionResult.isCorrect) {
          final errorType = _analyzeErrorType(questionResult, quiz.questions.length > i ? quiz.questions[i] : null);
          
          topicErrors.putIfAbsent(topicName, () => {});
          topicErrors[topicName]!.putIfAbsent(errorType, () => 0);
          topicErrors[topicName]![errorType] = topicErrors[topicName]![errorType]! + 1;
          
          topicMistakes.putIfAbsent(topicName, () => []);
          topicMistakes[topicName]!.add(_generateMistakeDescription(errorType, questionResult));
        }
      }
    }

    final List<ErrorPattern> patterns = [];
    for (final topicEntry in topicErrors.entries) {
      final topicName = topicEntry.key;
      final errors = topicEntry.value;
      
      for (final errorEntry in errors.entries) {
        final errorType = errorEntry.key;
        final frequency = errorEntry.value;
        
        if (frequency >= 3) { // Only consider patterns with 3+ occurrences
          patterns.add(ErrorPattern(
            id: 'ep_${topicName}_${errorType.name}_${DateTime.now().millisecondsSinceEpoch}',
            userId: 'current_user',
            topicName: topicName,
            errorType: errorType,
            frequency: frequency,
            commonMistakes: topicMistakes[topicName] ?? [],
            suggestedSolutions: _generateSuggestedSolutions(errorType, topicName),
            impactOnPerformance: _calculateErrorImpact(frequency, topicErrors[topicName]!.values.fold(0, (a, b) => a + b)),
            lastOccurrence: DateTime.now(),
          ));
        }
      }
    }

    _cachedErrorPatterns = patterns;
  }

  /// Generate Performance Predictions
  Future<void> _generatePerformancePredictions(List<QuizResult> results) async {
    final Map<String, List<QuizResult>> topicResults = {};
    
    // Group results by topic
    for (final result in results) {
      final topicName = _extractTopicFromTitle(result.quizTitle);
      topicResults.putIfAbsent(topicName, () => []).add(result);
    }

    final List<PerformancePrediction> predictions = [];
    
    for (final entry in topicResults.entries) {
      final topicName = entry.key;
      final topicResultsList = entry.value;
      
      if (topicResultsList.length < 3) continue; // Need at least 3 data points
      
      // Sort by date
      topicResultsList.sort((a, b) => (a.completedAt ?? DateTime.now()).compareTo(b.completedAt ?? DateTime.now()));
      
      // Calculate trend and predict next score
      final scores = topicResultsList.map((r) => r.scorePercentage).toList();
      final prediction = _predictNextScore(scores);
      final confidence = _calculatePredictionConfidence(scores);
      
      predictions.add(PerformancePrediction(
        id: 'pp_${topicName}_${DateTime.now().millisecondsSinceEpoch}',
        userId: 'current_user',
        topicName: topicName,
        predictedScore: prediction,
        confidenceLevel: confidence,
        predictionDate: DateTime.now(),
        targetDate: DateTime.now().add(const Duration(days: 7)),
        recommendedActions: _generateRecommendedActions(topicName, prediction, scores.last),
        accuracy: _calculatePredictionAccuracy(confidence),
      ));
    }

    _cachedPredictions = predictions;
  }

  // Helper methods
  String _extractTopicFromTitle(String title) {
    // Simple topic extraction - can be enhanced with NLP
    final words = title.split(' ');
    return words.length > 2 ? words.take(2).join(' ') : title;
  }

  KnowledgeLevel _calculateKnowledgeLevel(double masteryLevel) {
    if (masteryLevel >= 0.86) return KnowledgeLevel.excellent;
    if (masteryLevel >= 0.71) return KnowledgeLevel.good;
    if (masteryLevel >= 0.41) return KnowledgeLevel.average;
    return KnowledgeLevel.weak;
  }

  LearningTrend _calculateLearningTrend(List<LearningPoint> points) {
    if (points.length < 3) return LearningTrend.stable;
    
    final recentPoints = points.takeLast(5).toList();
    final scores = recentPoints.map((p) => p.score).toList();
    
    double trend = 0;
    for (int i = 1; i < scores.length; i++) {
      trend += scores[i] - scores[i - 1];
    }
    
    if (trend > 0.1) return LearningTrend.improving;
    if (trend < -0.1) return LearningTrend.declining;
    
    // Check for fluctuation
    double variance = 0;
    final mean = scores.reduce((a, b) => a + b) / scores.length;
    for (final score in scores) {
      variance += math.pow(score - mean, 2);
    }
    variance /= scores.length;
    
    return variance > 0.05 ? LearningTrend.fluctuating : LearningTrend.stable;
  }

  ErrorType _analyzeErrorType(QuestionResult questionResult, dynamic question) {
    // Simple error type analysis - can be enhanced with ML
    if (questionResult.timeSpentSeconds > 120) return ErrorType.timeManagement;
    if (questionResult.timeSpentSeconds < 10) return ErrorType.careless;
    return ErrorType.conceptual; // Default
  }

  bool _isImportantEvent(QuizResult result) {
    return result.scorePercentage >= 90 || result.scorePercentage <= 40;
  }

  String? _getEventDescription(QuizResult result) {
    if (result.scorePercentage >= 90) return 'إنجاز ممتاز!';
    if (result.scorePercentage <= 40) return 'يحتاج مراجعة';
    return null;
  }

  String _generateMistakeDescription(ErrorType errorType, QuestionResult questionResult) {
    switch (errorType) {
      case ErrorType.timeManagement:
        return 'استغرق وقت طويل في الإجابة';
      case ErrorType.careless:
        return 'إجابة سريعة جداً قد تكون غير مدروسة';
      case ErrorType.conceptual:
        return 'خطأ في فهم المفهوم الأساسي';
      default:
        return 'خطأ في الإجابة';
    }
  }

  List<String> _generateSuggestedSolutions(ErrorType errorType, String topicName) {
    switch (errorType) {
      case ErrorType.timeManagement:
        return [
          'تدرب على إدارة الوقت',
          'حدد وقت محدد لكل سؤال',
          'ابدأ بالأسئلة السهلة أولاً'
        ];
      case ErrorType.careless:
        return [
          'اقرأ السؤال بعناية',
          'راجع إجابتك قبل التأكيد',
          'تأكد من فهم المطلوب'
        ];
      case ErrorType.conceptual:
        return [
          'راجع المفاهيم الأساسية في $topicName',
          'حل المزيد من التمارين',
          'اطلب المساعدة من معلم'
        ];
      default:
        return ['راجع المادة وحاول مرة أخرى'];
    }
  }

  double _calculateErrorImpact(int errorFrequency, int totalErrors) {
    return totalErrors > 0 ? errorFrequency / totalErrors : 0.0;
  }

  double _predictNextScore(List<double> scores) {
    if (scores.length < 2) return scores.isNotEmpty ? scores.last : 50.0;
    
    // Simple linear regression
    final n = scores.length;
    final x = List.generate(n, (i) => i.toDouble());
    final y = scores;
    
    final sumX = x.reduce((a, b) => a + b);
    final sumY = y.reduce((a, b) => a + b);
    final sumXY = List.generate(n, (i) => x[i] * y[i]).reduce((a, b) => a + b);
    final sumX2 = x.map((xi) => xi * xi).reduce((a, b) => a + b);
    
    final slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    final intercept = (sumY - slope * sumX) / n;
    
    return math.max(0, math.min(100, slope * n + intercept));
  }

  double _calculatePredictionConfidence(List<double> scores) {
    if (scores.length < 3) return 0.5;
    
    // Calculate based on score stability
    final mean = scores.reduce((a, b) => a + b) / scores.length;
    double variance = 0;
    for (final score in scores) {
      variance += math.pow(score - mean, 2);
    }
    variance /= scores.length;
    
    // Lower variance = higher confidence
    return math.max(0.3, math.min(0.95, 1.0 - (variance / 1000)));
  }

  List<String> _generateRecommendedActions(String topicName, double predictedScore, double currentScore) {
    final List<String> actions = [];
    
    if (predictedScore < currentScore) {
      actions.add('راجع المفاهيم الأساسية في $topicName');
      actions.add('حل المزيد من التمارين التدريبية');
    } else if (predictedScore > currentScore + 10) {
      actions.add('استمر في الأداء الجيد');
      actions.add('تحدى نفسك بأسئلة أكثر صعوبة');
    } else {
      actions.add('حافظ على مستوى الممارسة الحالي');
    }
    
    return actions;
  }

  PredictionAccuracy _calculatePredictionAccuracy(double confidence) {
    if (confidence >= 0.95) return PredictionAccuracy.veryHigh;
    if (confidence >= 0.85) return PredictionAccuracy.high;
    if (confidence >= 0.70) return PredictionAccuracy.medium;
    return PredictionAccuracy.low;
  }

  // Getters for cached data
  KnowledgeMap? get knowledgeMap => _cachedKnowledgeMap;
  LearningCurve? get learningCurve => _cachedLearningCurve;
  List<ErrorPattern>? get errorPatterns => _cachedErrorPatterns;
  List<PerformancePrediction>? get performancePredictions => _cachedPredictions;
}

// Extension for takeLast method
extension TakeLastExtension<T> on List<T> {
  List<T> takeLast(int count) {
    if (count >= length) return this;
    return sublist(length - count);
  }
}
