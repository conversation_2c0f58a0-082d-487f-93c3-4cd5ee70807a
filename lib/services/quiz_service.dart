import 'dart:async';
import 'dart:io';
import 'package:uuid/uuid.dart';
import '../models/quiz_model.dart';
import '../models/question_model.dart';
import '../models/quiz_result_model.dart';
import '../services/pdf_service.dart';
import '../services/gemini_service.dart';
import '../services/storage_service.dart';


class QuizCreationRequest {
  final File pdfFile;
  final String title;
  final String description;
  final int questionCount;
  final QuestionDifficulty? preferredDifficulty;
  final String? category;
  final int timeLimit;

  QuizCreationRequest({
    required this.pdfFile,
    required this.title,
    required this.description,
    required this.questionCount,
    this.preferredDifficulty,
    this.category,
    this.timeLimit = 0,
  });
}

class QuizSession {
  final String id;
  final Quiz quiz;
  final DateTime startTime;
  final List<QuestionResult> answers;
  final Stopwatch stopwatch;
  
  QuizSession({
    required this.id,
    required this.quiz,
    required this.startTime,
    List<QuestionResult>? answers,
  }) : answers = answers ?? [],
       stopwatch = Stopwatch()..start();

  int get currentQuestionIndex => answers.length;
  bool get isCompleted => answers.length >= quiz.questions.length;
  Duration get elapsedTime => stopwatch.elapsed;
  
  Question? get currentQuestion {
    if (currentQuestionIndex < quiz.questions.length) {
      return quiz.questions[currentQuestionIndex];
    }
    return null;
  }
}

class QuizService {
  static final QuizService _instance = QuizService._internal();
  factory QuizService() => _instance;
  QuizService._internal();

  final PdfService _pdfService = PdfService();
  final GeminiService _geminiService = GeminiService();
  final StorageService _storageService = StorageService();
  final Uuid _uuid = const Uuid();

  QuizSession? _currentSession;

  /// Create a new quiz from PDF file
  Future<Quiz> createQuizFromPdf(QuizCreationRequest request) async {
    try {
      // Validate PDF file
      final isValidPdf = await _pdfService.validatePdfFile(request.pdfFile);
      if (!isValidPdf) {
        throw QuizServiceException('Invalid PDF file');
      }

      // Extract text from PDF
      final extractionResult = await _pdfService.extractTextFromPdf(request.pdfFile);
      
      // Preprocess text for better AI processing
      final processedText = _pdfService.preprocessText(extractionResult.text);

      // Generate quiz questions using AI
      final quizGenerationRequest = QuizGenerationRequest(
        pdfContent: processedText,
        questionCount: request.questionCount,
        preferredDifficulty: request.preferredDifficulty,
        category: request.category,
      );

      final generationResult = await _geminiService.generateQuiz(quizGenerationRequest);

      // Create quiz object
      final quiz = Quiz(
        id: _uuid.v4(),
        title: request.title,
        description: request.description,
        sourceFileName: extractionResult.fileName,
        sourcePdfPath: extractionResult.filePath,
        questions: generationResult.questions,
        status: QuizStatus.ready,
        createdAt: DateTime.now(),
        timeLimit: request.timeLimit,
        category: request.category,
        averageDifficulty: _calculateAverageDifficulty(generationResult.questions),
      );

      // Save quiz to storage
      await _storageService.saveQuiz(quiz);

      return quiz;

    } catch (e) {
      if (e is QuizServiceException) rethrow;
      throw QuizServiceException('Failed to create quiz: ${e.toString()}');
    }
  }

  /// Start a quiz session
  Future<QuizSession> startQuiz(String quizId) async {
    try {
      final quiz = await _storageService.getQuiz(quizId);
      if (quiz == null) {
        throw QuizServiceException('Quiz not found');
      }

      if (quiz.status != QuizStatus.ready) {
        throw QuizServiceException('Quiz is not ready to start');
      }

      _currentSession = QuizSession(
        id: _uuid.v4(),
        quiz: quiz,
        startTime: DateTime.now(),
      );

      return _currentSession!;

    } catch (e) {
      if (e is QuizServiceException) rethrow;
      throw QuizServiceException('Failed to start quiz: ${e.toString()}');
    }
  }

  /// Submit an answer for the current question
  Future<bool> submitAnswer(int selectedAnswerIndex) async {
    if (_currentSession == null) {
      throw QuizServiceException('No active quiz session');
    }

    final currentQuestion = _currentSession!.currentQuestion;
    if (currentQuestion == null) {
      throw QuizServiceException('No current question available');
    }

    final isCorrect = selectedAnswerIndex == currentQuestion.correctAnswerIndex;
    final timeSpent = _currentSession!.stopwatch.elapsed;

    final questionResult = QuestionResult(
      questionId: currentQuestion.id,
      selectedAnswerIndex: selectedAnswerIndex,
      isCorrect: isCorrect,
      timeSpentSeconds: timeSpent.inSeconds,
      answeredAt: DateTime.now(),
    );

    _currentSession!.answers.add(questionResult);
    _currentSession!.stopwatch.reset();
    _currentSession!.stopwatch.start();

    return isCorrect;
  }

  /// Complete the current quiz session
  Future<QuizResult> completeQuiz() async {
    if (_currentSession == null) {
      throw QuizServiceException('No active quiz session');
    }

    if (!_currentSession!.isCompleted) {
      throw QuizServiceException('Quiz is not completed yet');
    }

    try {
      _currentSession!.stopwatch.stop();

      final correctAnswers = _currentSession!.answers.where((a) => a.isCorrect).length;
      final totalQuestions = _currentSession!.quiz.questions.length;
      final scorePercentage = (correctAnswers / totalQuestions) * 100;
      final totalTime = _currentSession!.elapsedTime;

      // Generate AI feedback
      String? feedback;
      try {
        feedback = await _geminiService.generateQuizFeedback(
          questions: _currentSession!.quiz.questions,
          answers: _currentSession!.answers.map((a) => a.isCorrect).toList(),
          scorePercentage: scorePercentage,
          totalTime: totalTime,
        );
      } catch (e) {
        // Use default feedback if AI generation fails
        feedback = _generateDefaultFeedback(scorePercentage);
      }

      final quizResult = QuizResult(
        id: _uuid.v4(),
        quizId: _currentSession!.quiz.id,
        quizTitle: _currentSession!.quiz.title,
        questionResults: _currentSession!.answers,
        status: QuizResultStatus.completed,
        startedAt: _currentSession!.startTime,
        completedAt: DateTime.now(),
        totalTimeSeconds: totalTime.inSeconds,
        scorePercentage: scorePercentage,
        feedback: feedback,
        analytics: _generateAnalytics(_currentSession!),
      );

      // Save result to storage
      await _storageService.saveQuizResult(quizResult);

      // Clear current session
      _currentSession = null;

      return quizResult;

    } catch (e) {
      if (e is QuizServiceException) rethrow;
      throw QuizServiceException('Failed to complete quiz: ${e.toString()}');
    }
  }

  /// Get current quiz session
  QuizSession? getCurrentSession() => _currentSession;

  /// Abandon current quiz session
  Future<void> abandonQuiz() async {
    if (_currentSession == null) return;

    try {
      final quizResult = QuizResult(
        id: _uuid.v4(),
        quizId: _currentSession!.quiz.id,
        quizTitle: _currentSession!.quiz.title,
        questionResults: _currentSession!.answers,
        status: QuizResultStatus.abandoned,
        startedAt: _currentSession!.startTime,
        totalTimeSeconds: _currentSession!.elapsedTime.inSeconds,
        scorePercentage: 0.0,
      );

      await _storageService.saveQuizResult(quizResult);
      _currentSession = null;

    } catch (e) {
      // Log error but don't throw
      _currentSession = null;
    }
  }

  /// Get all quizzes
  Future<List<Quiz>> getAllQuizzes() async {
    return await _storageService.getAllQuizzes();
  }

  /// Get quiz by ID
  Future<Quiz?> getQuiz(String id) async {
    return await _storageService.getQuiz(id);
  }

  /// Get quiz results
  Future<List<QuizResult>> getQuizResults(String quizId) async {
    return await _storageService.getQuizResults(quizId);
  }

  /// Get all quiz results
  Future<List<QuizResult>> getAllQuizResults() async {
    return await _storageService.getAllQuizResults();
  }

  /// Delete quiz
  Future<void> deleteQuiz(String quizId) async {
    await _storageService.deleteQuiz(quizId);
  }

  /// Get user statistics
  Future<Map<String, dynamic>> getUserStatistics() async {
    return await _storageService.getUserStatistics();
  }

  /// Calculate average difficulty of questions
  QuestionDifficulty _calculateAverageDifficulty(List<Question> questions) {
    if (questions.isEmpty) return QuestionDifficulty.medium;

    final difficultyValues = questions.map((q) {
      switch (q.difficulty) {
        case QuestionDifficulty.easy:
          return 1;
        case QuestionDifficulty.medium:
          return 2;
        case QuestionDifficulty.hard:
          return 3;
      }
    }).toList();

    final average = difficultyValues.reduce((a, b) => a + b) / difficultyValues.length;

    if (average <= 1.5) return QuestionDifficulty.easy;
    if (average <= 2.5) return QuestionDifficulty.medium;
    return QuestionDifficulty.hard;
  }

  /// Generate default feedback
  String _generateDefaultFeedback(double scorePercentage) {
    if (scorePercentage >= 90) {
      return 'Excellent work! You demonstrated outstanding understanding of the material.';
    } else if (scorePercentage >= 75) {
      return 'Great job! You have a solid grasp of the concepts covered in this quiz.';
    } else if (scorePercentage >= 60) {
      return 'Good effort! Review the explanations to strengthen your understanding.';
    } else {
      return 'Keep practicing! Use the explanations to learn and improve for next time.';
    }
  }

  /// Generate analytics for quiz session
  Map<String, dynamic> _generateAnalytics(QuizSession session) {
    final answers = session.answers;
    final questions = session.quiz.questions;

    final difficultyPerformance = <String, Map<String, int>>{};
    final categoryPerformance = <String, Map<String, int>>{};

    for (int i = 0; i < answers.length && i < questions.length; i++) {
      final answer = answers[i];
      final question = questions[i];

      // Difficulty performance
      final difficulty = question.difficulty.name;
      difficultyPerformance[difficulty] ??= {'correct': 0, 'total': 0};
      difficultyPerformance[difficulty]!['total'] = 
          difficultyPerformance[difficulty]!['total']! + 1;
      if (answer.isCorrect) {
        difficultyPerformance[difficulty]!['correct'] = 
            difficultyPerformance[difficulty]!['correct']! + 1;
      }

      // Category performance
      if (question.category != null) {
        final category = question.category!;
        categoryPerformance[category] ??= {'correct': 0, 'total': 0};
        categoryPerformance[category]!['total'] = 
            categoryPerformance[category]!['total']! + 1;
        if (answer.isCorrect) {
          categoryPerformance[category]!['correct'] = 
              categoryPerformance[category]!['correct']! + 1;
        }
      }
    }

    return {
      'difficultyPerformance': difficultyPerformance,
      'categoryPerformance': categoryPerformance,
      'averageTimePerQuestion': session.elapsedTime.inSeconds / answers.length,
      'totalQuestions': questions.length,
      'answeredQuestions': answers.length,
    };
  }
}

class QuizServiceException implements Exception {
  final String message;
  
  QuizServiceException(this.message);
  
  @override
  String toString() => 'QuizServiceException: $message';
}
