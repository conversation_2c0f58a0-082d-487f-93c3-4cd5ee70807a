import 'dart:convert';

// Knowledge Map Models
class KnowledgeMap {
  final String id;
  final String userId;
  final Map<String, TopicKnowledge> topics;
  final DateTime lastUpdated;
  final Map<String, List<String>> topicRelations;

  const KnowledgeMap({
    required this.id,
    required this.userId,
    required this.topics,
    required this.lastUpdated,
    required this.topicRelations,
  });

  factory KnowledgeMap.fromMap(Map<String, dynamic> map) {
    return KnowledgeMap(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      topics: Map<String, TopicKnowledge>.from(
        (map['topics'] ?? {}).map((key, value) => 
          MapEntry(key, TopicKnowledge.fromMap(value))),
      ),
      lastUpdated: DateTime.parse(map['lastUpdated'] ?? DateTime.now().toIso8601String()),
      topicRelations: Map<String, List<String>>.from(
        (map['topicRelations'] ?? {}).map((key, value) => 
          MapEntry(key, List<String>.from(value))),
      ),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'topics': topics.map((key, value) => MapEntry(key, value.toMap())),
      'lastUpdated': lastUpdated.toIso8601String(),
      'topicRelations': topicRelations,
    };
  }

  String toJson() => json.encode(toMap());
  factory KnowledgeMap.fromJson(String source) => KnowledgeMap.fromMap(json.decode(source));
}

class TopicKnowledge {
  final String topicName;
  final double masteryLevel; // 0.0 to 1.0
  final int totalQuestions;
  final int correctAnswers;
  final double averageTime;
  final List<String> relatedQuizzes;
  final DateTime lastPracticed;
  final KnowledgeLevel level;

  const TopicKnowledge({
    required this.topicName,
    required this.masteryLevel,
    required this.totalQuestions,
    required this.correctAnswers,
    required this.averageTime,
    required this.relatedQuizzes,
    required this.lastPracticed,
    required this.level,
  });

  factory TopicKnowledge.fromMap(Map<String, dynamic> map) {
    return TopicKnowledge(
      topicName: map['topicName'] ?? '',
      masteryLevel: (map['masteryLevel'] ?? 0.0).toDouble(),
      totalQuestions: map['totalQuestions'] ?? 0,
      correctAnswers: map['correctAnswers'] ?? 0,
      averageTime: (map['averageTime'] ?? 0.0).toDouble(),
      relatedQuizzes: List<String>.from(map['relatedQuizzes'] ?? []),
      lastPracticed: DateTime.parse(map['lastPracticed'] ?? DateTime.now().toIso8601String()),
      level: KnowledgeLevel.values[map['level'] ?? 0],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'topicName': topicName,
      'masteryLevel': masteryLevel,
      'totalQuestions': totalQuestions,
      'correctAnswers': correctAnswers,
      'averageTime': averageTime,
      'relatedQuizzes': relatedQuizzes,
      'lastPracticed': lastPracticed.toIso8601String(),
      'level': level.index,
    };
  }
}

enum KnowledgeLevel {
  weak,     // 0-40% (أحمر)
  average,  // 41-70% (أصفر)
  good,     // 71-85% (أخضر فاتح)
  excellent // 86-100% (أخضر داكن)
}

// Learning Curve Models
class LearningCurve {
  final String id;
  final String userId;
  final List<LearningPoint> dataPoints;
  final DateTime startDate;
  final DateTime endDate;
  final LearningTrend trend;

  const LearningCurve({
    required this.id,
    required this.userId,
    required this.dataPoints,
    required this.startDate,
    required this.endDate,
    required this.trend,
  });

  factory LearningCurve.fromMap(Map<String, dynamic> map) {
    return LearningCurve(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      dataPoints: List<LearningPoint>.from(
        (map['dataPoints'] ?? []).map((x) => LearningPoint.fromMap(x)),
      ),
      startDate: DateTime.parse(map['startDate'] ?? DateTime.now().toIso8601String()),
      endDate: DateTime.parse(map['endDate'] ?? DateTime.now().toIso8601String()),
      trend: LearningTrend.values[map['trend'] ?? 0],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'dataPoints': dataPoints.map((x) => x.toMap()).toList(),
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'trend': trend.index,
    };
  }
}

class LearningPoint {
  final DateTime timestamp;
  final double score;
  final double accuracy;
  final double speed; // questions per minute
  final String quizId;
  final String topicName;
  final bool isImportantEvent;
  final String? eventDescription;

  const LearningPoint({
    required this.timestamp,
    required this.score,
    required this.accuracy,
    required this.speed,
    required this.quizId,
    required this.topicName,
    this.isImportantEvent = false,
    this.eventDescription,
  });

  factory LearningPoint.fromMap(Map<String, dynamic> map) {
    return LearningPoint(
      timestamp: DateTime.parse(map['timestamp'] ?? DateTime.now().toIso8601String()),
      score: (map['score'] ?? 0.0).toDouble(),
      accuracy: (map['accuracy'] ?? 0.0).toDouble(),
      speed: (map['speed'] ?? 0.0).toDouble(),
      quizId: map['quizId'] ?? '',
      topicName: map['topicName'] ?? '',
      isImportantEvent: map['isImportantEvent'] ?? false,
      eventDescription: map['eventDescription'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'score': score,
      'accuracy': accuracy,
      'speed': speed,
      'quizId': quizId,
      'topicName': topicName,
      'isImportantEvent': isImportantEvent,
      'eventDescription': eventDescription,
    };
  }
}

enum LearningTrend {
  improving,
  stable,
  declining,
  fluctuating
}

// Error Pattern Models
class ErrorPattern {
  final String id;
  final String userId;
  final String topicName;
  final ErrorType errorType;
  final int frequency;
  final List<String> commonMistakes;
  final List<String> suggestedSolutions;
  final double impactOnPerformance;
  final DateTime lastOccurrence;

  const ErrorPattern({
    required this.id,
    required this.userId,
    required this.topicName,
    required this.errorType,
    required this.frequency,
    required this.commonMistakes,
    required this.suggestedSolutions,
    required this.impactOnPerformance,
    required this.lastOccurrence,
  });

  factory ErrorPattern.fromMap(Map<String, dynamic> map) {
    return ErrorPattern(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      topicName: map['topicName'] ?? '',
      errorType: ErrorType.values[map['errorType'] ?? 0],
      frequency: map['frequency'] ?? 0,
      commonMistakes: List<String>.from(map['commonMistakes'] ?? []),
      suggestedSolutions: List<String>.from(map['suggestedSolutions'] ?? []),
      impactOnPerformance: (map['impactOnPerformance'] ?? 0.0).toDouble(),
      lastOccurrence: DateTime.parse(map['lastOccurrence'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'topicName': topicName,
      'errorType': errorType.index,
      'frequency': frequency,
      'commonMistakes': commonMistakes,
      'suggestedSolutions': suggestedSolutions,
      'impactOnPerformance': impactOnPerformance,
      'lastOccurrence': lastOccurrence.toIso8601String(),
    };
  }
}

enum ErrorType {
  conceptual,      // خطأ في فهم المفهوم
  procedural,      // خطأ في الإجراء
  computational,   // خطأ في الحساب
  reading,         // خطأ في قراءة السؤال
  timeManagement,  // خطأ في إدارة الوقت
  careless        // خطأ بسبب عدم التركيز
}

// Performance Prediction Models
class PerformancePrediction {
  final String id;
  final String userId;
  final String topicName;
  final double predictedScore;
  final double confidenceLevel;
  final DateTime predictionDate;
  final DateTime targetDate;
  final List<String> recommendedActions;
  final PredictionAccuracy accuracy;

  const PerformancePrediction({
    required this.id,
    required this.userId,
    required this.topicName,
    required this.predictedScore,
    required this.confidenceLevel,
    required this.predictionDate,
    required this.targetDate,
    required this.recommendedActions,
    required this.accuracy,
  });

  factory PerformancePrediction.fromMap(Map<String, dynamic> map) {
    return PerformancePrediction(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      topicName: map['topicName'] ?? '',
      predictedScore: (map['predictedScore'] ?? 0.0).toDouble(),
      confidenceLevel: (map['confidenceLevel'] ?? 0.0).toDouble(),
      predictionDate: DateTime.parse(map['predictionDate'] ?? DateTime.now().toIso8601String()),
      targetDate: DateTime.parse(map['targetDate'] ?? DateTime.now().toIso8601String()),
      recommendedActions: List<String>.from(map['recommendedActions'] ?? []),
      accuracy: PredictionAccuracy.values[map['accuracy'] ?? 0],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'topicName': topicName,
      'predictedScore': predictedScore,
      'confidenceLevel': confidenceLevel,
      'predictionDate': predictionDate.toIso8601String(),
      'targetDate': targetDate.toIso8601String(),
      'recommendedActions': recommendedActions,
      'accuracy': accuracy.index,
    };
  }
}

enum PredictionAccuracy {
  low,     // أقل من 70%
  medium,  // 70-85%
  high,    // 85-95%
  veryHigh // أكثر من 95%
}
