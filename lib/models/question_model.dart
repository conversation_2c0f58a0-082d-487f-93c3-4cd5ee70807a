import 'dart:convert';

enum QuestionType {
  multipleChoice,
}

enum QuestionDifficulty {
  easy,
  medium,
  hard,
}

class Question {
  final String id;
  final String text;
  final QuestionType type;
  final List<String> options;
  final int correctAnswerIndex;
  final String explanation;
  final QuestionDifficulty difficulty;
  final String? category;
  final DateTime createdAt;

  Question({
    required this.id,
    required this.text,
    required this.type,
    required this.options,
    required this.correctAnswerIndex,
    required this.explanation,
    required this.difficulty,
    this.category,
    required this.createdAt,
  });

  String get correctAnswer => options[correctAnswerIndex];

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'text': text,
      'type': type.name,
      'options': json.encode(options),
      'correctAnswerIndex': correctAnswerIndex,
      'explanation': explanation,
      'difficulty': difficulty.name,
      'category': category,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory Question.fromMap(Map<String, dynamic> map) {
    return Question(
      id: map['id'] ?? '',
      text: map['text'] ?? '',
      type: QuestionType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => QuestionType.multipleChoice,
      ),
      options: List<String>.from(json.decode(map['options'] ?? '[]')),
      correctAnswerIndex: map['correctAnswerIndex']?.toInt() ?? 0,
      explanation: map['explanation'] ?? '',
      difficulty: QuestionDifficulty.values.firstWhere(
        (e) => e.name == map['difficulty'],
        orElse: () => QuestionDifficulty.medium,
      ),
      category: map['category'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
    );
  }

  String toJson() => json.encode(toMap());

  factory Question.fromJson(String source) => Question.fromMap(json.decode(source));

  Question copyWith({
    String? id,
    String? text,
    QuestionType? type,
    List<String>? options,
    int? correctAnswerIndex,
    String? explanation,
    QuestionDifficulty? difficulty,
    String? category,
    DateTime? createdAt,
  }) {
    return Question(
      id: id ?? this.id,
      text: text ?? this.text,
      type: type ?? this.type,
      options: options ?? this.options,
      correctAnswerIndex: correctAnswerIndex ?? this.correctAnswerIndex,
      explanation: explanation ?? this.explanation,
      difficulty: difficulty ?? this.difficulty,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Question && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Question(id: $id, text: $text, type: $type, difficulty: $difficulty)';
  }
}
