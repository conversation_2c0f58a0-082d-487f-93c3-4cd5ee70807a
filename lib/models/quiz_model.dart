import 'dart:convert';
import 'question_model.dart';

enum QuizStatus {
  draft,
  ready,
  inProgress,
  completed,
}

class Quiz {
  final String id;
  final String title;
  final String description;
  final String sourceFileName;
  final String? sourcePdfPath;
  final List<Question> questions;
  final QuizStatus status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final int timeLimit; // in minutes, 0 means no limit
  final String? category;
  final QuestionDifficulty? averageDifficulty;

  Quiz({
    required this.id,
    required this.title,
    required this.description,
    required this.sourceFileName,
    this.sourcePdfPath,
    required this.questions,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    this.timeLimit = 0,
    this.category,
    this.averageDifficulty,
  });

  int get totalQuestions => questions.length;

  QuestionDifficulty get calculatedAverageDifficulty {
    if (questions.isEmpty) return QuestionDifficulty.medium;
    
    final difficultyValues = questions.map((q) {
      switch (q.difficulty) {
        case QuestionDifficulty.easy:
          return 1;
        case QuestionDifficulty.medium:
          return 2;
        case QuestionDifficulty.hard:
          return 3;
      }
    }).toList();

    final average = difficultyValues.reduce((a, b) => a + b) / difficultyValues.length;
    
    if (average <= 1.5) return QuestionDifficulty.easy;
    if (average <= 2.5) return QuestionDifficulty.medium;
    return QuestionDifficulty.hard;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'sourceFileName': sourceFileName,
      'sourcePdfPath': sourcePdfPath,
      'questions': json.encode(questions.map((q) => q.toMap()).toList()),
      'status': status.name,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'timeLimit': timeLimit,
      'category': category,
      'averageDifficulty': averageDifficulty?.name,
    };
  }

  factory Quiz.fromMap(Map<String, dynamic> map) {
    final questionsJson = map['questions'] ?? '[]';
    final questionsList = json.decode(questionsJson) as List;
    
    return Quiz(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      sourceFileName: map['sourceFileName'] ?? '',
      sourcePdfPath: map['sourcePdfPath'],
      questions: questionsList.map((q) => Question.fromMap(q)).toList(),
      status: QuizStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => QuizStatus.draft,
      ),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: map['updatedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt'])
          : null,
      timeLimit: map['timeLimit']?.toInt() ?? 0,
      category: map['category'],
      averageDifficulty: map['averageDifficulty'] != null
          ? QuestionDifficulty.values.firstWhere(
              (e) => e.name == map['averageDifficulty'],
              orElse: () => QuestionDifficulty.medium,
            )
          : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory Quiz.fromJson(String source) => Quiz.fromMap(json.decode(source));

  Quiz copyWith({
    String? id,
    String? title,
    String? description,
    String? sourceFileName,
    String? sourcePdfPath,
    List<Question>? questions,
    QuizStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? timeLimit,
    String? category,
    QuestionDifficulty? averageDifficulty,
  }) {
    return Quiz(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      sourceFileName: sourceFileName ?? this.sourceFileName,
      sourcePdfPath: sourcePdfPath ?? this.sourcePdfPath,
      questions: questions ?? this.questions,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      timeLimit: timeLimit ?? this.timeLimit,
      category: category ?? this.category,
      averageDifficulty: averageDifficulty ?? this.averageDifficulty,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Quiz && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Quiz(id: $id, title: $title, questions: ${questions.length}, status: $status)';
  }
}
