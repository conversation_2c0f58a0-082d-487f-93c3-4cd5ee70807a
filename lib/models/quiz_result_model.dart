import 'dart:convert';
import 'question_model.dart';
import 'quiz_model.dart';

class QuestionResult {
  final String questionId;
  final int selectedAnswerIndex;
  final bool isCorrect;
  final int timeSpentSeconds;
  final DateTime answeredAt;

  QuestionResult({
    required this.questionId,
    required this.selectedAnswerIndex,
    required this.isCorrect,
    required this.timeSpentSeconds,
    required this.answeredAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'questionId': questionId,
      'selectedAnswerIndex': selectedAnswerIndex,
      'isCorrect': isCorrect,
      'timeSpentSeconds': timeSpentSeconds,
      'answeredAt': answeredAt.millisecondsSinceEpoch,
    };
  }

  factory QuestionResult.fromMap(Map<String, dynamic> map) {
    return QuestionResult(
      questionId: map['questionId'] ?? '',
      selectedAnswerIndex: map['selectedAnswerIndex']?.toInt() ?? -1,
      isCorrect: map['isCorrect'] ?? false,
      timeSpentSeconds: map['timeSpentSeconds']?.toInt() ?? 0,
      answeredAt: DateTime.fromMillisecondsSinceEpoch(map['answeredAt'] ?? 0),
    );
  }
}

enum QuizResultStatus {
  inProgress,
  completed,
  abandoned,
}

class QuizResult {
  final String id;
  final String quizId;
  final String quizTitle;
  final List<QuestionResult> questionResults;
  final QuizResultStatus status;
  final DateTime startedAt;
  final DateTime? completedAt;
  final int totalTimeSeconds;
  final double scorePercentage;
  final String? feedback;
  final Map<String, dynamic>? analytics;

  QuizResult({
    required this.id,
    required this.quizId,
    required this.quizTitle,
    required this.questionResults,
    required this.status,
    required this.startedAt,
    this.completedAt,
    required this.totalTimeSeconds,
    required this.scorePercentage,
    this.feedback,
    this.analytics,
  });

  int get totalQuestions => questionResults.length;
  int get correctAnswers => questionResults.where((r) => r.isCorrect).length;
  int get incorrectAnswers => totalQuestions - correctAnswers;

  String get grade {
    if (scorePercentage >= 90) return 'A+';
    if (scorePercentage >= 85) return 'A';
    if (scorePercentage >= 80) return 'B+';
    if (scorePercentage >= 75) return 'B';
    if (scorePercentage >= 70) return 'C+';
    if (scorePercentage >= 65) return 'C';
    if (scorePercentage >= 60) return 'D+';
    if (scorePercentage >= 55) return 'D';
    return 'F';
  }

  String get performanceLevel {
    if (scorePercentage >= 85) return 'Excellent';
    if (scorePercentage >= 75) return 'Good';
    if (scorePercentage >= 65) return 'Average';
    if (scorePercentage >= 50) return 'Below Average';
    return 'Poor';
  }

  Duration get averageTimePerQuestion {
    if (totalQuestions == 0) return Duration.zero;
    return Duration(seconds: totalTimeSeconds ~/ totalQuestions);
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'quizId': quizId,
      'quizTitle': quizTitle,
      'questionResults': json.encode(questionResults.map((r) => r.toMap()).toList()),
      'status': status.name,
      'startedAt': startedAt.millisecondsSinceEpoch,
      'completedAt': completedAt?.millisecondsSinceEpoch,
      'totalTimeSeconds': totalTimeSeconds,
      'scorePercentage': scorePercentage,
      'feedback': feedback,
      'analytics': analytics != null ? json.encode(analytics) : null,
    };
  }

  factory QuizResult.fromMap(Map<String, dynamic> map) {
    final questionResultsJson = map['questionResults'] ?? '[]';
    final questionResultsList = json.decode(questionResultsJson) as List;
    
    return QuizResult(
      id: map['id'] ?? '',
      quizId: map['quizId'] ?? '',
      quizTitle: map['quizTitle'] ?? '',
      questionResults: questionResultsList.map((r) => QuestionResult.fromMap(r)).toList(),
      status: QuizResultStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => QuizResultStatus.inProgress,
      ),
      startedAt: DateTime.fromMillisecondsSinceEpoch(map['startedAt'] ?? 0),
      completedAt: map['completedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['completedAt'])
          : null,
      totalTimeSeconds: map['totalTimeSeconds']?.toInt() ?? 0,
      scorePercentage: map['scorePercentage']?.toDouble() ?? 0.0,
      feedback: map['feedback'],
      analytics: map['analytics'] != null 
          ? json.decode(map['analytics']) as Map<String, dynamic>
          : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory QuizResult.fromJson(String source) => QuizResult.fromMap(json.decode(source));

  QuizResult copyWith({
    String? id,
    String? quizId,
    String? quizTitle,
    List<QuestionResult>? questionResults,
    QuizResultStatus? status,
    DateTime? startedAt,
    DateTime? completedAt,
    int? totalTimeSeconds,
    double? scorePercentage,
    String? feedback,
    Map<String, dynamic>? analytics,
  }) {
    return QuizResult(
      id: id ?? this.id,
      quizId: quizId ?? this.quizId,
      quizTitle: quizTitle ?? this.quizTitle,
      questionResults: questionResults ?? this.questionResults,
      status: status ?? this.status,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      totalTimeSeconds: totalTimeSeconds ?? this.totalTimeSeconds,
      scorePercentage: scorePercentage ?? this.scorePercentage,
      feedback: feedback ?? this.feedback,
      analytics: analytics ?? this.analytics,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuizResult && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'QuizResult(id: $id, quizId: $quizId, score: $scorePercentage%, status: $status)';
  }
}
