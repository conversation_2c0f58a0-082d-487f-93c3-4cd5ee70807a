import 'dart:convert';

enum AppThemeMode {
  system,
  light,
  dark,
}

enum Language {
  arabic,
  english,
}

enum QuizDifficulty {
  easy,
  medium,
  hard,
  mixed,
}

enum NotificationFrequency {
  never,
  daily,
  weekly,
  monthly,
}

class AppSettings {
  // Appearance Settings
  final AppThemeMode themeMode;
  final Language language;
  final bool enableAnimations;
  final double fontSize;
  final bool enableRTL;

  // Quiz Settings
  final int defaultQuestionCount;
  final int defaultTimeLimit; // in minutes, 0 = no limit
  final QuizDifficulty defaultDifficulty;
  final bool autoSubmitAnswers;
  final bool showCorrectAnswers;
  final bool enableHints;
  final bool shuffleQuestions;
  final bool shuffleAnswers;

  // PDF Settings
  final int maxPdfSizeMB;
  final bool extractImages;
  final bool processFormulas;
  final bool enhanceTextQuality;

  // AI Settings
  final String geminiApiKey;
  final double aiTemperature;
  final int maxTokens;
  final bool enableContextualQuestions;
  final bool generateExplanations;

  // Notification Settings
  final bool enableNotifications;
  final NotificationFrequency reminderFrequency;
  final bool soundEnabled;
  final bool vibrationEnabled;

  // Privacy Settings
  final bool saveQuizHistory;
  final bool shareAnalytics;
  final bool enableCrashReporting;
  final bool autoBackup;

  // Performance Settings
  final bool enableCaching;
  final int cacheSize; // in MB
  final bool preloadContent;
  final bool optimizeImages;

  // Accessibility Settings
  final bool enableScreenReader;
  final bool highContrast;
  final bool largeText;
  final bool reduceMotion;

  const AppSettings({
    // Appearance
    this.themeMode = AppThemeMode.system,
    this.language = Language.arabic,
    this.enableAnimations = true,
    this.fontSize = 16.0,
    this.enableRTL = true,

    // Quiz
    this.defaultQuestionCount = 10,
    this.defaultTimeLimit = 30,
    this.defaultDifficulty = QuizDifficulty.medium,
    this.autoSubmitAnswers = false,
    this.showCorrectAnswers = true,
    this.enableHints = true,
    this.shuffleQuestions = false,
    this.shuffleAnswers = true,

    // PDF
    this.maxPdfSizeMB = 50,
    this.extractImages = true,
    this.processFormulas = true,
    this.enhanceTextQuality = true,

    // AI
    this.geminiApiKey = 'AIzaSyA0C1cNjFlLfKUB0ovHS84fyucjODjsvQI',
    this.aiTemperature = 0.7,
    this.maxTokens = 8192,
    this.enableContextualQuestions = true,
    this.generateExplanations = true,

    // Notifications
    this.enableNotifications = true,
    this.reminderFrequency = NotificationFrequency.weekly,
    this.soundEnabled = true,
    this.vibrationEnabled = true,

    // Privacy
    this.saveQuizHistory = true,
    this.shareAnalytics = false,
    this.enableCrashReporting = true,
    this.autoBackup = false,

    // Performance
    this.enableCaching = true,
    this.cacheSize = 100,
    this.preloadContent = false,
    this.optimizeImages = true,

    // Accessibility
    this.enableScreenReader = false,
    this.highContrast = false,
    this.largeText = false,
    this.reduceMotion = false,
  });

  AppSettings copyWith({
    AppThemeMode? themeMode,
    Language? language,
    bool? enableAnimations,
    double? fontSize,
    bool? enableRTL,
    int? defaultQuestionCount,
    int? defaultTimeLimit,
    QuizDifficulty? defaultDifficulty,
    bool? autoSubmitAnswers,
    bool? showCorrectAnswers,
    bool? enableHints,
    bool? shuffleQuestions,
    bool? shuffleAnswers,
    int? maxPdfSizeMB,
    bool? extractImages,
    bool? processFormulas,
    bool? enhanceTextQuality,
    String? geminiApiKey,
    double? aiTemperature,
    int? maxTokens,
    bool? enableContextualQuestions,
    bool? generateExplanations,
    bool? enableNotifications,
    NotificationFrequency? reminderFrequency,
    bool? soundEnabled,
    bool? vibrationEnabled,
    bool? saveQuizHistory,
    bool? shareAnalytics,
    bool? enableCrashReporting,
    bool? autoBackup,
    bool? enableCaching,
    int? cacheSize,
    bool? preloadContent,
    bool? optimizeImages,
    bool? enableScreenReader,
    bool? highContrast,
    bool? largeText,
    bool? reduceMotion,
  }) {
    return AppSettings(
      themeMode: themeMode ?? this.themeMode,
      language: language ?? this.language,
      enableAnimations: enableAnimations ?? this.enableAnimations,
      fontSize: fontSize ?? this.fontSize,
      enableRTL: enableRTL ?? this.enableRTL,
      defaultQuestionCount: defaultQuestionCount ?? this.defaultQuestionCount,
      defaultTimeLimit: defaultTimeLimit ?? this.defaultTimeLimit,
      defaultDifficulty: defaultDifficulty ?? this.defaultDifficulty,
      autoSubmitAnswers: autoSubmitAnswers ?? this.autoSubmitAnswers,
      showCorrectAnswers: showCorrectAnswers ?? this.showCorrectAnswers,
      enableHints: enableHints ?? this.enableHints,
      shuffleQuestions: shuffleQuestions ?? this.shuffleQuestions,
      shuffleAnswers: shuffleAnswers ?? this.shuffleAnswers,
      maxPdfSizeMB: maxPdfSizeMB ?? this.maxPdfSizeMB,
      extractImages: extractImages ?? this.extractImages,
      processFormulas: processFormulas ?? this.processFormulas,
      enhanceTextQuality: enhanceTextQuality ?? this.enhanceTextQuality,
      geminiApiKey: geminiApiKey ?? this.geminiApiKey,
      aiTemperature: aiTemperature ?? this.aiTemperature,
      maxTokens: maxTokens ?? this.maxTokens,
      enableContextualQuestions: enableContextualQuestions ?? this.enableContextualQuestions,
      generateExplanations: generateExplanations ?? this.generateExplanations,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      reminderFrequency: reminderFrequency ?? this.reminderFrequency,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      saveQuizHistory: saveQuizHistory ?? this.saveQuizHistory,
      shareAnalytics: shareAnalytics ?? this.shareAnalytics,
      enableCrashReporting: enableCrashReporting ?? this.enableCrashReporting,
      autoBackup: autoBackup ?? this.autoBackup,
      enableCaching: enableCaching ?? this.enableCaching,
      cacheSize: cacheSize ?? this.cacheSize,
      preloadContent: preloadContent ?? this.preloadContent,
      optimizeImages: optimizeImages ?? this.optimizeImages,
      enableScreenReader: enableScreenReader ?? this.enableScreenReader,
      highContrast: highContrast ?? this.highContrast,
      largeText: largeText ?? this.largeText,
      reduceMotion: reduceMotion ?? this.reduceMotion,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'themeMode': themeMode.name,
      'language': language.name,
      'enableAnimations': enableAnimations,
      'fontSize': fontSize,
      'enableRTL': enableRTL,
      'defaultQuestionCount': defaultQuestionCount,
      'defaultTimeLimit': defaultTimeLimit,
      'defaultDifficulty': defaultDifficulty.name,
      'autoSubmitAnswers': autoSubmitAnswers,
      'showCorrectAnswers': showCorrectAnswers,
      'enableHints': enableHints,
      'shuffleQuestions': shuffleQuestions,
      'shuffleAnswers': shuffleAnswers,
      'maxPdfSizeMB': maxPdfSizeMB,
      'extractImages': extractImages,
      'processFormulas': processFormulas,
      'enhanceTextQuality': enhanceTextQuality,
      'geminiApiKey': geminiApiKey,
      'aiTemperature': aiTemperature,
      'maxTokens': maxTokens,
      'enableContextualQuestions': enableContextualQuestions,
      'generateExplanations': generateExplanations,
      'enableNotifications': enableNotifications,
      'reminderFrequency': reminderFrequency.name,
      'soundEnabled': soundEnabled,
      'vibrationEnabled': vibrationEnabled,
      'saveQuizHistory': saveQuizHistory,
      'shareAnalytics': shareAnalytics,
      'enableCrashReporting': enableCrashReporting,
      'autoBackup': autoBackup,
      'enableCaching': enableCaching,
      'cacheSize': cacheSize,
      'preloadContent': preloadContent,
      'optimizeImages': optimizeImages,
      'enableScreenReader': enableScreenReader,
      'highContrast': highContrast,
      'largeText': largeText,
      'reduceMotion': reduceMotion,
    };
  }

  factory AppSettings.fromMap(Map<String, dynamic> map) {
    return AppSettings(
      themeMode: AppThemeMode.values.firstWhere(
        (e) => e.name == map['themeMode'],
        orElse: () => AppThemeMode.system,
      ),
      language: Language.values.firstWhere(
        (e) => e.name == map['language'],
        orElse: () => Language.arabic,
      ),
      enableAnimations: map['enableAnimations'] ?? true,
      fontSize: map['fontSize']?.toDouble() ?? 16.0,
      enableRTL: map['enableRTL'] ?? true,
      defaultQuestionCount: map['defaultQuestionCount'] ?? 10,
      defaultTimeLimit: map['defaultTimeLimit'] ?? 30,
      defaultDifficulty: QuizDifficulty.values.firstWhere(
        (e) => e.name == map['defaultDifficulty'],
        orElse: () => QuizDifficulty.medium,
      ),
      autoSubmitAnswers: map['autoSubmitAnswers'] ?? false,
      showCorrectAnswers: map['showCorrectAnswers'] ?? true,
      enableHints: map['enableHints'] ?? true,
      shuffleQuestions: map['shuffleQuestions'] ?? false,
      shuffleAnswers: map['shuffleAnswers'] ?? true,
      maxPdfSizeMB: map['maxPdfSizeMB'] ?? 50,
      extractImages: map['extractImages'] ?? true,
      processFormulas: map['processFormulas'] ?? true,
      enhanceTextQuality: map['enhanceTextQuality'] ?? true,
      geminiApiKey: map['geminiApiKey'] ?? 'AIzaSyA0C1cNjFlLfKUB0ovHS84fyucjODjsvQI',
      aiTemperature: map['aiTemperature']?.toDouble() ?? 0.7,
      maxTokens: map['maxTokens'] ?? 8192,
      enableContextualQuestions: map['enableContextualQuestions'] ?? true,
      generateExplanations: map['generateExplanations'] ?? true,
      enableNotifications: map['enableNotifications'] ?? true,
      reminderFrequency: NotificationFrequency.values.firstWhere(
        (e) => e.name == map['reminderFrequency'],
        orElse: () => NotificationFrequency.weekly,
      ),
      soundEnabled: map['soundEnabled'] ?? true,
      vibrationEnabled: map['vibrationEnabled'] ?? true,
      saveQuizHistory: map['saveQuizHistory'] ?? true,
      shareAnalytics: map['shareAnalytics'] ?? false,
      enableCrashReporting: map['enableCrashReporting'] ?? true,
      autoBackup: map['autoBackup'] ?? false,
      enableCaching: map['enableCaching'] ?? true,
      cacheSize: map['cacheSize'] ?? 100,
      preloadContent: map['preloadContent'] ?? false,
      optimizeImages: map['optimizeImages'] ?? true,
      enableScreenReader: map['enableScreenReader'] ?? false,
      highContrast: map['highContrast'] ?? false,
      largeText: map['largeText'] ?? false,
      reduceMotion: map['reduceMotion'] ?? false,
    );
  }

  String toJson() => json.encode(toMap());

  factory AppSettings.fromJson(String source) => AppSettings.fromMap(json.decode(source));

  @override
  String toString() {
    return 'AppSettings(themeMode: $themeMode, language: $language, defaultQuestionCount: $defaultQuestionCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppSettings &&
        other.themeMode == themeMode &&
        other.language == language &&
        other.enableAnimations == enableAnimations &&
        other.fontSize == fontSize &&
        other.enableRTL == enableRTL;
  }

  @override
  int get hashCode {
    return themeMode.hashCode ^
        language.hashCode ^
        enableAnimations.hashCode ^
        fontSize.hashCode ^
        enableRTL.hashCode;
  }
}
