import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'app.dart';

import 'services/gemini_service.dart';
import 'config/app_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Initialize services
  await _initializeServices();

  // Request permissions
  await _requestPermissions();

  runApp(
    const ProviderScope(
      child: QuePdfApp(),
    ),
  );
}

Future<void> _initializeServices() async {
  try {
    // Initialize Gemini service with API key (this doesn't require platform channels)
    final apiKey = EnvironmentConfig.geminiApiKey;
    if (apiKey.isNotEmpty) {
      GeminiService().initialize(apiKey);
    }

    // Note: StorageService will be initialized lazily when first used
    // to avoid platform channel issues during app startup
  } catch (e) {
    debugPrint('Failed to initialize services: $e');
  }
}

Future<void> _requestPermissions() async {
  try {
    // Request storage permission for file access
    await Permission.storage.request();
    await Permission.manageExternalStorage.request();
  } catch (e) {
    debugPrint('Failed to request permissions: $e');
  }
}
