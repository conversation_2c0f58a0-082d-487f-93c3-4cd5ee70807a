import 'package:flutter/material.dart';

enum AppLanguage {
  arabic('ar', 'العربية'),
  english('en', 'English');

  const AppLanguage(this.code, this.name);
  final String code;
  final String name;
}

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations) ??
        AppLocalizations(const Locale('ar'));
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static const List<Locale> supportedLocales = [
    Locale('ar'),
    Locale('en'),
  ];

  bool get isArabic => locale.languageCode == 'ar';
  bool get isEnglish => locale.languageCode == 'en';

  // App basics
  String get appName => 'QuePDF';
  
  String get appDescription => isArabic 
      ? 'محول PDF إلى اختبارات احترافي مع توليد الأسئلة بالذكاء الاصطناعي'
      : 'Professional PDF to Quiz converter with AI-powered question generation';

  // Navigation
  String get home => isArabic ? 'الرئيسية' : 'Home';
  String get quizzes => isArabic ? 'الاختبارات' : 'Quizzes';
  String get history => isArabic ? 'التاريخ' : 'History';
  String get settings => isArabic ? 'الإعدادات' : 'Settings';

  // Actions
  String get createQuiz => isArabic ? 'إنشاء اختبار' : 'Create Quiz';
  String get uploadPdf => isArabic ? 'رفع ملف PDF' : 'Upload PDF';
  String get selectPdf => isArabic ? 'اختيار ملف PDF' : 'Select PDF File';
  String get generateQuiz => isArabic ? 'توليد الاختبار' : 'Generate Quiz';
  String get startQuiz => isArabic ? 'بدء الاختبار' : 'Start Quiz';
  String get retakeQuiz => isArabic ? 'إعادة الاختبار' : 'Retake Quiz';

  // Quiz configuration
  String get quizTitle => isArabic ? 'عنوان الاختبار' : 'Quiz Title';
  String get quizDescription => isArabic ? 'وصف الاختبار' : 'Quiz Description';
  String get questionCount => isArabic ? 'عدد الأسئلة' : 'Number of Questions';
  String get timeLimit => isArabic ? 'الحد الزمني (دقائق)' : 'Time Limit (minutes)';
  String get difficulty => isArabic ? 'مستوى الصعوبة' : 'Difficulty';
  String get category => isArabic ? 'الفئة' : 'Category';

  // Difficulty levels
  String get easy => isArabic ? 'سهل' : 'Easy';
  String get medium => isArabic ? 'متوسط' : 'Medium';
  String get hard => isArabic ? 'صعب' : 'Hard';

  // Common
  String get optional => isArabic ? 'اختياري' : 'Optional';
  String get noTimeLimit => isArabic ? 'بدون حد زمني' : 'No Time Limit';
  String get processing => isArabic ? 'جاري المعالجة...' : 'Processing...';
  String get generatingQuiz => isArabic ? 'جاري توليد الاختبار...' : 'Generating Quiz...';

  // Quiz taking
  String get nextQuestion => isArabic ? 'السؤال التالي' : 'Next Question';
  String get previousQuestion => isArabic ? 'السؤال السابق' : 'Previous Question';
  String get submitAnswer => isArabic ? 'إرسال الإجابة' : 'Submit Answer';
  String get finishQuiz => isArabic ? 'إنهاء الاختبار' : 'Finish Quiz';

  // Results
  String get results => isArabic ? 'النتائج' : 'Results';
  String get score => isArabic ? 'النقاط' : 'Score';
  String get correctAnswers => isArabic ? 'الإجابات الصحيحة' : 'Correct Answers';
  String get incorrectAnswers => isArabic ? 'الإجابات الخاطئة' : 'Incorrect Answers';
  String get totalQuestions => isArabic ? 'إجمالي الأسئلة' : 'Total Questions';
  String get timeSpent => isArabic ? 'الوقت المستغرق' : 'Time Spent';
  String get grade => isArabic ? 'الدرجة' : 'Grade';
  String get performance => isArabic ? 'الأداء' : 'Performance';

  // Performance levels
  String get excellent => isArabic ? 'ممتاز' : 'Excellent';
  String get veryGood => isArabic ? 'جيد جداً' : 'Very Good';
  String get good => isArabic ? 'جيد' : 'Good';
  String get satisfactory => isArabic ? 'مقبول' : 'Satisfactory';
  String get needsImprovement => isArabic ? 'يحتاج تحسين' : 'Needs Improvement';

  // History
  String get recentQuizzes => isArabic ? 'الاختبارات الحديثة' : 'Recent Quizzes';
  String get completedQuizzes => isArabic ? 'الاختبارات المكتملة' : 'Completed Quizzes';
  String get noQuizzesYet => isArabic ? 'لا توجد اختبارات بعد' : 'No quizzes yet';
  String get createFirstQuiz => isArabic ? 'أنشئ اختبارك الأول برفع ملف PDF' : 'Create your first quiz by uploading a PDF file';

  // Statistics
  String get statistics => isArabic ? 'الإحصائيات' : 'Statistics';
  String get totalQuizzes => isArabic ? 'إجمالي الاختبارات' : 'Total Quizzes';
  String get averageScore => isArabic ? 'متوسط النقاط' : 'Average Score';
  String get bestScore => isArabic ? 'أفضل نقاط' : 'Best Score';

  // Settings
  String get language => isArabic ? 'اللغة' : 'Language';
  String get theme => isArabic ? 'السمة' : 'Theme';
  String get darkMode => isArabic ? 'الوضع الداكن' : 'Dark Mode';
  String get lightMode => isArabic ? 'الوضع الفاتح' : 'Light Mode';
  String get systemMode => isArabic ? 'وضع النظام' : 'System';

  // API Configuration
  String get apiConfiguration => isArabic ? 'إعدادات API' : 'API Configuration';
  String get geminiApiKey => isArabic ? 'مفتاح Gemini API' : 'Gemini API Key';
  String get apiKeyDescription => isArabic 
      ? 'أدخل مفتاح Google Gemini API لتفعيل توليد الاختبارات.'
      : 'Enter your Google Gemini API key to enable quiz generation.';
  String get apiKeyPlaceholder => isArabic ? 'أدخل مفتاح API...' : 'Enter API key...';
  String get testConnection => isArabic ? 'اختبار الاتصال' : 'Test Connection';
  String get connectionSuccessful => isArabic ? 'تم الاتصال بنجاح!' : 'Connection successful!';
  String get connectionFailed => isArabic 
      ? 'فشل الاتصال. يرجى التحقق من مفتاح API.'
      : 'Connection failed. Please check your API key.';

  // About
  String get about => isArabic ? 'حول' : 'About';
  String get version => isArabic ? 'الإصدار' : 'Version';
  String get developer => isArabic ? 'المطور' : 'Developer';
  String get licenses => isArabic ? 'التراخيص' : 'Licenses';

  // Common actions
  String get ok => isArabic ? 'موافق' : 'OK';
  String get cancel => isArabic ? 'إلغاء' : 'Cancel';
  String get yes => isArabic ? 'نعم' : 'Yes';
  String get no => isArabic ? 'لا' : 'No';
  String get retry => isArabic ? 'إعادة المحاولة' : 'Retry';
  String get close => isArabic ? 'إغلاق' : 'Close';
  String get save => isArabic ? 'حفظ' : 'Save';
  String get delete => isArabic ? 'حذف' : 'Delete';
  String get edit => isArabic ? 'تعديل' : 'Edit';
  String get share => isArabic ? 'مشاركة' : 'Share';

  // Error messages
  String get error => isArabic ? 'خطأ' : 'Error';
  String get success => isArabic ? 'نجح' : 'Success';
  String get warning => isArabic ? 'تحذير' : 'Warning';
  String get info => isArabic ? 'معلومات' : 'Information';

  String get errorGeneric => isArabic 
      ? 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.'
      : 'An unexpected error occurred. Please try again.';
  String get errorNetwork => isArabic 
      ? 'خطأ في الشبكة. يرجى التحقق من اتصال الإنترنت.'
      : 'Network error. Please check your internet connection.';
  String get errorPdfParsing => isArabic 
      ? 'فشل في تحليل ملف PDF. يرجى التأكد من أنه ملف PDF صالح.'
      : 'Failed to parse PDF file. Please ensure it\'s a valid PDF.';
  String get errorQuizGeneration => isArabic 
      ? 'فشل في توليد الاختبار. يرجى المحاولة مرة أخرى.'
      : 'Failed to generate quiz. Please try again.';
  String get errorStorage => isArabic 
      ? 'خطأ في التخزين. يرجى التحقق من المساحة المتاحة.'
      : 'Storage error. Please check available space.';

  // Success messages
  String get successQuizGenerated => isArabic ? 'تم توليد الاختبار بنجاح!' : 'Quiz generated successfully!';
  String get successQuizCompleted => isArabic ? 'تم إكمال الاختبار بنجاح!' : 'Quiz completed successfully!';
  String get successPdfUploaded => isArabic ? 'تم رفع ملف PDF بنجاح!' : 'PDF uploaded successfully!';

  // Data management
  String get backup => isArabic ? 'نسخ احتياطي' : 'Backup';
  String get restore => isArabic ? 'استعادة' : 'Restore';
  String get exportData => isArabic ? 'تصدير البيانات' : 'Export Data';
  String get importData => isArabic ? 'استيراد البيانات' : 'Import Data';

  // Question helpers
  String questionNumber(int number) => isArabic ? 'السؤال $number' : 'Question $number';

  // Time formatting
  String get minutes => isArabic ? 'دقائق' : 'minutes';
  String get seconds => isArabic ? 'ثواني' : 'seconds';
  String get hours => isArabic ? 'ساعات' : 'hours';

  // Dates
  String get createdAt => isArabic ? 'تم الإنشاء في' : 'Created at';
  String get completedAt => isArabic ? 'تم الإكمال في' : 'Completed at';
  String get lastModified => isArabic ? 'آخر تعديل' : 'Last modified';

  // Search and filter
  String get searchQuizzes => isArabic ? 'البحث في الاختبارات...' : 'Search quizzes...';
  String get filterByDifficulty => isArabic ? 'تصفية حسب الصعوبة' : 'Filter by difficulty';
  String get filterByCategory => isArabic ? 'تصفية حسب الفئة' : 'Filter by category';
  String get sortBy => isArabic ? 'ترتيب حسب' : 'Sort by';
  String get sortByDate => isArabic ? 'التاريخ' : 'Date';
  String get sortByScore => isArabic ? 'النقاط' : 'Score';
  String get sortByTitle => isArabic ? 'العنوان' : 'Title';

  // Empty states
  String get noResults => isArabic ? 'لم يتم العثور على نتائج' : 'No results found';
  String get noQuizResults => isArabic ? 'لم يتم العثور على نتائج اختبارات' : 'No quiz results found';
  String get viewResults => isArabic ? 'عرض النتائج' : 'View Results';
  String get deleteQuiz => isArabic ? 'حذف الاختبار' : 'Delete Quiz';
  String get shareResults => isArabic ? 'مشاركة النتائج' : 'Share Results';

  // Settings specific
  String get appearance => isArabic ? 'المظهر' : 'Appearance';
  String get quiz => isArabic ? 'الاختبار' : 'Quiz';
  String get notifications => isArabic ? 'الإشعارات' : 'Notifications';
  String get more => isArabic ? 'المزيد' : 'More';
  String get themeMode => isArabic ? 'وضع السمة' : 'Theme Mode';
  String get exportSettings => isArabic ? 'تصدير الإعدادات' : 'Export Settings';
  String get importSettings => isArabic ? 'استيراد الإعدادات' : 'Import Settings';
  String get resetSettings => isArabic ? 'إعادة تعيين الإعدادات' : 'Reset Settings';
  String get viewAll => isArabic ? 'عرض الكل' : 'View All';
  String get upload => isArabic ? 'رفع' : 'Upload';

  // Advanced Analytics
  String get advancedAnalytics => isArabic ? 'التحليلات المتقدمة' : 'Advanced Analytics';
  String get knowledgeMap => isArabic ? 'خريطة المعرفة' : 'Knowledge Map';
  String get learningCurves => isArabic ? 'منحنيات التعلم' : 'Learning Curves';
  String get errorAnalysis => isArabic ? 'تحليل الأخطاء' : 'Error Analysis';
  String get performancePredictions => isArabic ? 'تنبؤات الأداء' : 'Performance Predictions';
  String get insights => isArabic ? 'الرؤى' : 'Insights';
  String get recommendations => isArabic ? 'التوصيات' : 'Recommendations';
  String get trends => isArabic ? 'الاتجاهات' : 'Trends';
  String get patterns => isArabic ? 'الأنماط' : 'Patterns';
  String get masteryLevel => isArabic ? 'مستوى الإتقان' : 'Mastery Level';
  String get weakTopics => isArabic ? 'المواضيع الضعيفة' : 'Weak Topics';
  String get strongTopics => isArabic ? 'المواضيع القوية' : 'Strong Topics';
  String get improvementAreas => isArabic ? 'مجالات التحسين' : 'Improvement Areas';
  String get studyPlan => isArabic ? 'خطة الدراسة' : 'Study Plan';
  String get practiceMore => isArabic ? 'تدرب أكثر' : 'Practice More';
  String get reviewMaterial => isArabic ? 'راجع المادة' : 'Review Material';
  String get timeManagement => isArabic ? 'إدارة الوقت' : 'Time Management';
  String get accuracyRate => isArabic ? 'معدل الدقة' : 'Accuracy Rate';
  String get responseSpeed => isArabic ? 'سرعة الاستجابة' : 'Response Speed';
  String get confidenceLevel => isArabic ? 'مستوى الثقة' : 'Confidence Level';
  String get predictionAccuracy => isArabic ? 'دقة التنبؤ' : 'Prediction Accuracy';
  String get welcomeMessage => isArabic ? 'مرحباً بك في QuePDF' : 'Welcome to QuePDF';
  String get welcomeDescription => isArabic ? 'محول PDF إلى اختبارات ذكي' : 'Smart PDF to Quiz Converter';
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['ar', 'en'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
