// مساعد الترجمة المؤقت حتى يتم تكوين flutter gen-l10n بشكل صحيح
class TempLocalizations {
  // أساسيات التطبيق
  static const String appName = 'QuePDF';
  static const String appDescription = 'محول PDF إلى اختبارات احترافي مع توليد الأسئلة بالذكاء الاصطناعي';

  // التنقل
  static const String home = 'الرئيسية';
  static const String quizzes = 'الاختبارات';
  static const String history = 'التاريخ';
  static const String settings = 'الإعدادات';

  // الإجراءات
  static const String createQuiz = 'إنشاء اختبار';
  static const String uploadPdf = 'رفع ملف PDF';
  static const String selectPdf = 'اختيار ملف PDF';
  static const String generateQuiz = 'توليد الاختبار';
  static const String startQuiz = 'بدء الاختبار';
  static const String retakeQuiz = 'إعادة الاختبار';
  
  // إعدادات الاختبار
  static const String quizTitle = 'عنوان الاختبار';
  static const String quizDescription = 'وصف الاختبار';
  static const String questionCount = 'عدد الأسئلة';
  static const String timeLimit = 'الحد الزمني (دقائق)';
  static const String difficulty = 'مستوى الصعوبة';
  static const String category = 'الفئة';

  // مستويات الصعوبة
  static const String easy = 'سهل';
  static const String medium = 'متوسط';
  static const String hard = 'صعب';

  // عام
  static const String optional = 'اختياري';
  static const String noTimeLimit = 'بدون حد زمني';
  static const String processing = 'جاري المعالجة...';
  static const String generatingQuiz = 'جاري توليد الاختبار...';
  
  // إجراء الاختبار
  static const String nextQuestion = 'السؤال التالي';
  static const String previousQuestion = 'السؤال السابق';
  static const String submitAnswer = 'إرسال الإجابة';
  static const String finishQuiz = 'إنهاء الاختبار';

  // النتائج
  static const String results = 'النتائج';
  static const String score = 'النقاط';
  static const String correctAnswers = 'الإجابات الصحيحة';
  static const String incorrectAnswers = 'الإجابات الخاطئة';
  static const String totalQuestions = 'إجمالي الأسئلة';
  static const String timeSpent = 'الوقت المستغرق';
  static const String grade = 'الدرجة';
  static const String performance = 'الأداء';
  
  // مستويات الأداء
  static const String excellent = 'ممتاز';
  static const String veryGood = 'جيد جداً';
  static const String good = 'جيد';
  static const String satisfactory = 'مقبول';
  static const String needsImprovement = 'يحتاج تحسين';

  // التاريخ
  static const String recentQuizzes = 'الاختبارات الحديثة';
  static const String completedQuizzes = 'الاختبارات المكتملة';
  static const String noQuizzesYet = 'لا توجد اختبارات بعد';
  static const String createFirstQuiz = 'أنشئ اختبارك الأول برفع ملف PDF';

  // الإحصائيات
  static const String statistics = 'الإحصائيات';
  static const String totalQuizzes = 'إجمالي الاختبارات';
  static const String averageScore = 'متوسط النقاط';
  static const String bestScore = 'أفضل نقاط';
  
  // الإعدادات
  static const String language = 'اللغة';
  static const String theme = 'السمة';
  static const String darkMode = 'الوضع الداكن';
  static const String lightMode = 'الوضع الفاتح';
  static const String systemMode = 'وضع النظام';

  // إعدادات API
  static const String apiConfiguration = 'إعدادات API';
  static const String geminiApiKey = 'مفتاح Gemini API';
  static const String apiKeyDescription = 'أدخل مفتاح Google Gemini API لتفعيل توليد الاختبارات.';
  static const String apiKeyPlaceholder = 'أدخل مفتاح API...';
  static const String testConnection = 'اختبار الاتصال';
  static const String connectionSuccessful = 'تم الاتصال بنجاح!';
  static const String connectionFailed = 'فشل الاتصال. يرجى التحقق من مفتاح API.';
  
  // حول
  static const String about = 'حول';
  static const String version = 'الإصدار';
  static const String developer = 'المطور';
  static const String licenses = 'التراخيص';

  // الإجراءات الشائعة
  static const String ok = 'موافق';
  static const String cancel = 'إلغاء';
  static const String yes = 'نعم';
  static const String no = 'لا';
  static const String retry = 'إعادة المحاولة';
  static const String close = 'إغلاق';
  static const String save = 'حفظ';
  static const String delete = 'حذف';
  static const String edit = 'تعديل';
  static const String share = 'مشاركة';
  
  // رسائل الخطأ
  static const String error = 'خطأ';
  static const String success = 'نجح';
  static const String warning = 'تحذير';
  static const String info = 'معلومات';

  static const String errorGeneric = 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
  static const String errorNetwork = 'خطأ في الشبكة. يرجى التحقق من اتصال الإنترنت.';
  static const String errorPdfParsing = 'فشل في تحليل ملف PDF. يرجى التأكد من أنه ملف PDF صالح.';
  static const String errorQuizGeneration = 'فشل في توليد الاختبار. يرجى المحاولة مرة أخرى.';
  static const String errorStorage = 'خطأ في التخزين. يرجى التحقق من المساحة المتاحة.';

  // رسائل النجاح
  static const String successQuizGenerated = 'تم توليد الاختبار بنجاح!';
  static const String successQuizCompleted = 'تم إكمال الاختبار بنجاح!';
  static const String successPdfUploaded = 'تم رفع ملف PDF بنجاح!';
  
  // إدارة البيانات
  static const String backup = 'نسخ احتياطي';
  static const String restore = 'استعادة';
  static const String exportData = 'تصدير البيانات';
  static const String importData = 'استيراد البيانات';

  // مساعدات الأسئلة
  static String questionNumber(int number) => 'السؤال $number';

  // تنسيق الوقت
  static const String minutes = 'دقائق';
  static const String seconds = 'ثواني';
  static const String hours = 'ساعات';

  // التواريخ
  static const String createdAt = 'تم الإنشاء في';
  static const String completedAt = 'تم الإكمال في';
  static const String lastModified = 'آخر تعديل';
  
  // البحث والتصفية
  static const String searchQuizzes = 'البحث في الاختبارات...';
  static const String filterByDifficulty = 'تصفية حسب الصعوبة';
  static const String filterByCategory = 'تصفية حسب الفئة';
  static const String sortBy = 'ترتيب حسب';
  static const String sortByDate = 'التاريخ';
  static const String sortByScore = 'النقاط';
  static const String sortByTitle = 'العنوان';

  // الحالات الفارغة
  static const String noResults = 'لم يتم العثور على نتائج';
  static const String noQuizResults = 'لم يتم العثور على نتائج اختبارات';
  static const String viewResults = 'عرض النتائج';
  static const String deleteQuiz = 'حذف الاختبار';
  static const String shareResults = 'مشاركة النتائج';

  // Settings
  static const String appearance = 'المظهر';
  static const String quiz = 'الاختبار';
  static const String notifications = 'الإشعارات';
  static const String more = 'المزيد';
  static const String themeMode = 'وضع السمة';
  static const String exportSettings = 'تصدير الإعدادات';
  static const String importSettings = 'استيراد الإعدادات';
  static const String resetSettings = 'إعادة تعيين الإعدادات';
  static const String viewAll = 'عرض الكل';
  static const String upload = 'رفع';

  // التحليلات المتقدمة
  static const String advancedAnalytics = 'التحليلات المتقدمة';
  static const String knowledgeMap = 'خريطة المعرفة';
  static const String learningCurves = 'منحنيات التعلم';
  static const String errorAnalysis = 'تحليل الأخطاء';
  static const String performancePredictions = 'تنبؤات الأداء';
  static const String insights = 'الرؤى';
  static const String recommendations = 'التوصيات';
  static const String trends = 'الاتجاهات';
  static const String patterns = 'الأنماط';
  static const String masteryLevel = 'مستوى الإتقان';
  static const String weakTopics = 'المواضيع الضعيفة';
  static const String strongTopics = 'المواضيع القوية';
  static const String improvementAreas = 'مجالات التحسين';
  static const String studyPlan = 'خطة الدراسة';
  static const String practiceMore = 'تدرب أكثر';
  static const String reviewMaterial = 'راجع المادة';
  static const String timeManagement = 'إدارة الوقت';
  static const String accuracyRate = 'معدل الدقة';
  static const String responseSpeed = 'سرعة الاستجابة';
  static const String confidenceLevel = 'مستوى الثقة';
  static const String predictionAccuracy = 'دقة التنبؤ';
  static const String welcomeMessage = 'مرحباً بك في QuePDF';
  static const String welcomeDescription = 'محول PDF إلى اختبارات ذكي';
}
