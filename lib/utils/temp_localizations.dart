// Temporary localization helper until flutter gen-l10n is properly configured
class TempLocalizations {
  // App basics
  static const String appName = 'QuePDF';
  static const String appDescription = 'Professional PDF to Quiz converter with AI-powered question generation';
  
  // Navigation
  static const String home = 'Home';
  static const String quizzes = 'Quizzes';
  static const String history = 'History';
  static const String settings = 'Settings';
  
  // Actions
  static const String createQuiz = 'Create Quiz';
  static const String uploadPdf = 'Upload PDF';
  static const String selectPdf = 'Select PDF File';
  static const String generateQuiz = 'Generate Quiz';
  static const String startQuiz = 'Start Quiz';
  static const String retakeQuiz = 'Retake Quiz';
  
  // Quiz configuration
  static const String quizTitle = 'Quiz Title';
  static const String quizDescription = 'Quiz Description';
  static const String questionCount = 'Number of Questions';
  static const String timeLimit = 'Time Limit (minutes)';
  static const String difficulty = 'Difficulty';
  static const String category = 'Category';
  
  // Difficulty levels
  static const String easy = 'Easy';
  static const String medium = 'Medium';
  static const String hard = 'Hard';
  
  // Common
  static const String optional = 'Optional';
  static const String noTimeLimit = 'No Time Limit';
  static const String processing = 'Processing...';
  static const String generatingQuiz = 'Generating Quiz...';
  
  // Quiz taking
  static const String nextQuestion = 'Next Question';
  static const String previousQuestion = 'Previous Question';
  static const String submitAnswer = 'Submit Answer';
  static const String finishQuiz = 'Finish Quiz';
  
  // Results
  static const String results = 'Results';
  static const String score = 'Score';
  static const String correctAnswers = 'Correct Answers';
  static const String incorrectAnswers = 'Incorrect Answers';
  static const String totalQuestions = 'Total Questions';
  static const String timeSpent = 'Time Spent';
  static const String grade = 'Grade';
  static const String performance = 'Performance';
  
  // Performance levels
  static const String excellent = 'Excellent';
  static const String veryGood = 'Very Good';
  static const String good = 'Good';
  static const String satisfactory = 'Satisfactory';
  static const String needsImprovement = 'Needs Improvement';
  
  // History
  static const String recentQuizzes = 'Recent Quizzes';
  static const String completedQuizzes = 'Completed Quizzes';
  static const String noQuizzesYet = 'No quizzes yet';
  static const String createFirstQuiz = 'Create your first quiz by uploading a PDF file';
  
  // Statistics
  static const String statistics = 'Statistics';
  static const String totalQuizzes = 'Total Quizzes';
  static const String averageScore = 'Average Score';
  static const String bestScore = 'Best Score';
  
  // Settings
  static const String language = 'Language';
  static const String theme = 'Theme';
  static const String darkMode = 'Dark Mode';
  static const String lightMode = 'Light Mode';
  static const String systemMode = 'System';
  
  // API Configuration
  static const String apiConfiguration = 'API Configuration';
  static const String geminiApiKey = 'Gemini API Key';
  static const String apiKeyDescription = 'Enter your Google Gemini API key to enable quiz generation.';
  static const String apiKeyPlaceholder = 'Enter API key...';
  static const String testConnection = 'Test Connection';
  static const String connectionSuccessful = 'Connection successful!';
  static const String connectionFailed = 'Connection failed. Please check your API key.';
  
  // About
  static const String about = 'About';
  static const String version = 'Version';
  static const String developer = 'Developer';
  static const String licenses = 'Licenses';
  
  // Common actions
  static const String ok = 'OK';
  static const String cancel = 'Cancel';
  static const String yes = 'Yes';
  static const String no = 'No';
  static const String retry = 'Retry';
  static const String close = 'Close';
  static const String save = 'Save';
  static const String delete = 'Delete';
  static const String edit = 'Edit';
  static const String share = 'Share';
  
  // Error messages
  static const String error = 'Error';
  static const String success = 'Success';
  static const String warning = 'Warning';
  static const String info = 'Information';
  
  static const String errorGeneric = 'An unexpected error occurred. Please try again.';
  static const String errorNetwork = 'Network error. Please check your internet connection.';
  static const String errorPdfParsing = 'Failed to parse PDF file. Please ensure it\'s a valid PDF.';
  static const String errorQuizGeneration = 'Failed to generate quiz. Please try again.';
  static const String errorStorage = 'Storage error. Please check available space.';
  
  // Success messages
  static const String successQuizGenerated = 'Quiz generated successfully!';
  static const String successQuizCompleted = 'Quiz completed successfully!';
  static const String successPdfUploaded = 'PDF uploaded successfully!';
  
  // Data management
  static const String backup = 'Backup';
  static const String restore = 'Restore';
  static const String exportData = 'Export Data';
  static const String importData = 'Import Data';
  
  // Question helpers
  static String questionNumber(int number) => 'Question $number';
  
  // Time formatting
  static const String minutes = 'minutes';
  static const String seconds = 'seconds';
  static const String hours = 'hours';
  
  // Dates
  static const String createdAt = 'Created at';
  static const String completedAt = 'Completed at';
  static const String lastModified = 'Last modified';
  
  // Search and filter
  static const String searchQuizzes = 'Search quizzes...';
  static const String filterByDifficulty = 'Filter by difficulty';
  static const String filterByCategory = 'Filter by category';
  static const String sortBy = 'Sort by';
  static const String sortByDate = 'Date';
  static const String sortByScore = 'Score';
  static const String sortByTitle = 'Title';
  
  // Empty states
  static const String noResults = 'No results found';
  static const String noQuizResults = 'No quiz results found';
  static const String viewResults = 'View Results';
  static const String deleteQuiz = 'Delete Quiz';
  static const String shareResults = 'Share Results';
}
