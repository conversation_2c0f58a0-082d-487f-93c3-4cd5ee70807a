import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../config/app_config.dart';
import '../services/storage_service.dart';
import '../models/quiz_result_model.dart';
import '../models/quiz_model.dart';
import '../utils/temp_localizations.dart';

// Provider for quiz result
final quizResultProvider = FutureProvider.autoDispose
    .family<QuizResult?, String>((ref, resultId) async {
  try {
    final storageService = StorageService();
    await storageService.initialize();
    final results = await storageService.getAllQuizResults();
    return results.where((r) => r.id == resultId).firstOrNull;
  } catch (e) {
    return null;
  }
});

// Provider for quiz details
final quizDetailsProvider = FutureProvider.autoDispose
    .family<Quiz?, String>((ref, quizId) async {
  try {
    final storageService = StorageService();
    await storageService.initialize();
    return await storageService.getQuiz(quizId);
  } catch (e) {
    return null;
  }
});

class ResultsScreen extends ConsumerWidget {
  final String resultId;

  const ResultsScreen({super.key, required this.resultId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final resultAsync = ref.watch(quizResultProvider(resultId));

    return Scaffold(
      appBar: AppBar(
        title: Text(TempLocalizations.results),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareResults(context, resultAsync.value),
          ),
        ],
      ),
      body: resultAsync.when(
        data: (result) {
          if (result == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: theme.colorScheme.error,
                  ),
                  const SizedBox(height: AppConfig.paddingM),
                  Text(
                    'Result not found',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: AppConfig.paddingL),
                  ElevatedButton(
                    onPressed: () => context.go('/'),
                    child: const Text('Go to Home'),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConfig.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Score Overview
                _buildScoreOverview(context, theme, result),
                const SizedBox(height: AppConfig.paddingL),

                // Performance Metrics
                _buildPerformanceMetrics(context, theme, result),
                const SizedBox(height: AppConfig.paddingL),

                // Feedback Section
                if (result.feedback != null) ...[
                  _buildFeedbackSection(context, theme, result),
                  const SizedBox(height: AppConfig.paddingL),
                ],

                // Question Review
                _buildQuestionReview(context, theme, result, ref),
                const SizedBox(height: AppConfig.paddingL),

                // Action Buttons
                _buildActionButtons(context, result),
              ],
            ),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: AppConfig.paddingM),
              Text(
                'Error: $error',
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConfig.paddingL),
              ElevatedButton(
                onPressed: () => context.go('/'),
                child: const Text('Go to Home'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildScoreOverview(
    BuildContext context,
    ThemeData theme,
    QuizResult result,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          children: [
            // Score Circle
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    _getScoreColor(result.scorePercentage),
                    _getScoreColor(result.scorePercentage).withOpacity(0.7),
                  ],
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${result.scorePercentage.toInt()}%',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      result.grade,
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: AppConfig.paddingL),

            // Performance Level
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConfig.paddingL,
                vertical: AppConfig.paddingM,
              ),
              decoration: BoxDecoration(
                color: _getScoreColor(result.scorePercentage).withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConfig.radiusL),
              ),
              child: Text(
                result.performanceLevel,
                style: theme.textTheme.titleLarge?.copyWith(
                  color: _getScoreColor(result.scorePercentage),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: AppConfig.paddingM),

            // Quiz Title
            Text(
              result.quizTitle,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceMetrics(
    BuildContext context,
    ThemeData theme,
    QuizResult result,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Performance Metrics',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.paddingM),
            
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    context,
                    TempLocalizations.correctAnswers,
                    '${result.correctAnswers}',
                    Icons.check_circle,
                    AppConfig.successColor,
                  ),
                ),
                const SizedBox(width: AppConfig.paddingM),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    TempLocalizations.incorrectAnswers,
                    '${result.incorrectAnswers}',
                    Icons.cancel,
                    AppConfig.errorColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConfig.paddingM),
            
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    context,
                    TempLocalizations.totalQuestions,
                    '${result.totalQuestions}',
                    Icons.quiz,
                    theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(width: AppConfig.paddingM),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    TempLocalizations.timeSpent,
                    _formatDuration(Duration(seconds: result.totalTimeSeconds)),
                    Icons.timer,
                    AppConfig.warningColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConfig.radiusM),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 32,
          ),
          const SizedBox(height: AppConfig.paddingS),
          Text(
            value,
            style: theme.textTheme.titleLarge?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConfig.paddingXS),
          Text(
            title,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFeedbackSection(
    BuildContext context,
    ThemeData theme,
    QuizResult result,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.feedback,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: AppConfig.paddingS),
                Text(
                  'AI Feedback',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConfig.paddingM),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConfig.paddingM),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(AppConfig.radiusM),
              ),
              child: Text(
                result.feedback!,
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onPrimaryContainer,
                  height: 1.5,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuestionReview(
    BuildContext context,
    ThemeData theme,
    QuizResult result,
    WidgetRef ref,
  ) {
    final quizAsync = ref.watch(quizDetailsProvider(result.quizId));
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Question Review',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.paddingM),
            
            quizAsync.when(
              data: (quiz) {
                if (quiz == null) return const Text('Quiz not found');
                
                return Column(
                  children: result.questionResults.asMap().entries.map((entry) {
                    final index = entry.key;
                    final questionResult = entry.value;
                    final question = quiz.questions[index];
                    
                    return _buildQuestionReviewItem(
                      context,
                      theme,
                      question,
                      questionResult,
                      index + 1,
                    );
                  }).toList(),
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('Error loading questions: $error'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuestionReviewItem(
    BuildContext context,
    ThemeData theme,
    dynamic question,
    QuestionResult questionResult,
    int questionNumber,
  ) {
    // Safely extract question data
    final questionText = question?.text ?? 'السؤال غير متوفر';
    final options = question?.options ?? <String>[];
    final correctAnswerIndex = (question?.correctAnswerIndex ?? 0) as int;

    final selectedAnswer = questionResult.selectedAnswerIndex < options.length
        ? options[questionResult.selectedAnswerIndex]
        : 'غير محدد';
    final correctAnswer = correctAnswerIndex < options.length
        ? options[correctAnswerIndex]
        : 'غير محدد';

    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.paddingM),
      padding: const EdgeInsets.all(AppConfig.paddingM),
      decoration: BoxDecoration(
        border: Border.all(
          color: questionResult.isCorrect
              ? AppConfig.successColor
              : AppConfig.errorColor,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(AppConfig.radiusM),
        color: questionResult.isCorrect
            ? AppConfig.successColor.withOpacity(0.05)
            : AppConfig.errorColor.withOpacity(0.05),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: questionResult.isCorrect
                      ? AppConfig.successColor
                      : AppConfig.errorColor,
                  borderRadius: BorderRadius.circular(AppConfig.radiusS),
                ),
                child: Icon(
                  questionResult.isCorrect ? Icons.check : Icons.close,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppConfig.paddingS),
              Expanded(
                child: Text(
                  'السؤال $questionNumber',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: questionResult.isCorrect
                        ? AppConfig.successColor
                        : AppConfig.errorColor,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConfig.paddingS,
                  vertical: AppConfig.paddingXS,
                ),
                decoration: BoxDecoration(
                  color: questionResult.isCorrect
                      ? AppConfig.successColor.withOpacity(0.2)
                      : AppConfig.errorColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppConfig.radiusS),
                ),
                child: Text(
                  questionResult.isCorrect ? 'صحيح' : 'خطأ',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: questionResult.isCorrect
                        ? AppConfig.successColor
                        : AppConfig.errorColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConfig.paddingM),

          // Question Text
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppConfig.paddingM),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
              borderRadius: BorderRadius.circular(AppConfig.radiusS),
            ),
            child: Text(
              questionText,
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
                height: 1.4,
              ),
            ),
          ),
          const SizedBox(height: AppConfig.paddingM),

          // Your Answer
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppConfig.paddingM),
            decoration: BoxDecoration(
              color: questionResult.isCorrect
                  ? AppConfig.successColor.withOpacity(0.1)
                  : AppConfig.errorColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppConfig.radiusS),
              border: Border.all(
                color: questionResult.isCorrect
                    ? AppConfig.successColor.withOpacity(0.3)
                    : AppConfig.errorColor.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.person,
                      size: 16,
                      color: questionResult.isCorrect
                          ? AppConfig.successColor
                          : AppConfig.errorColor,
                    ),
                    const SizedBox(width: AppConfig.paddingXS),
                    Text(
                      'إجابتك:',
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: questionResult.isCorrect
                            ? AppConfig.successColor
                            : AppConfig.errorColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConfig.paddingXS),
                Text(
                  '${String.fromCharCode(65 + questionResult.selectedAnswerIndex)}) $selectedAnswer',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),

          // Correct Answer (if wrong)
          if (!questionResult.isCorrect) ...[
            const SizedBox(height: AppConfig.paddingS),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConfig.paddingM),
              decoration: BoxDecoration(
                color: AppConfig.successColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConfig.radiusS),
                border: Border.all(
                  color: AppConfig.successColor.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb,
                        size: 16,
                        color: AppConfig.successColor,
                      ),
                      const SizedBox(width: AppConfig.paddingXS),
                      Text(
                        'الإجابة الصحيحة:',
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppConfig.successColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConfig.paddingXS),
                  Text(
                    '${String.fromCharCode(65 + correctAnswerIndex)}) $correctAnswer',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      height: 1.3,
                      color: AppConfig.successColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Time spent
          const SizedBox(height: AppConfig.paddingS),
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: AppConfig.paddingXS),
              Text(
                'الوقت المستغرق: ${questionResult.timeSpentSeconds} ثانية',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    QuizResult result,
  ) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => context.push('/quiz/${result.quizId}'),
            child: Text(TempLocalizations.retakeQuiz),
          ),
        ),
        const SizedBox(height: AppConfig.paddingS),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: () => context.go('/'),
            child: const Text('Go to Home'),
          ),
        ),
      ],
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 85) return AppConfig.successColor;
    if (score >= 70) return AppConfig.warningColor;
    return AppConfig.errorColor;
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    }
    return '${seconds}s';
  }

  void _shareResults(BuildContext context, QuizResult? result) {
    if (result == null) return;
    
    final shareText = '''
🎯 Quiz Results: ${result.quizTitle}
📊 Score: ${result.scorePercentage.toInt()}% (${result.grade})
✅ Correct: ${result.correctAnswers}/${result.totalQuestions}
⏱️ Time: ${_formatDuration(Duration(seconds: result.totalTimeSeconds))}
📱 Generated by QuePDF App
''';
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Share text copied: $shareText'),
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
