import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../config/app_config.dart';
import '../services/quiz_service.dart';
import '../models/quiz_model.dart';
import '../models/question_model.dart';
import '../utils/temp_localizations.dart';

// Provider for quiz session
final quizSessionProvider = StateNotifierProvider.autoDispose
    .family<QuizSessionNotifier, QuizSessionState, String>(
  (ref, quizId) => QuizSessionNotifier(quizId),
);

class QuizSessionState {
  final Quiz? quiz;
  final QuizSession? session;
  final int currentQuestionIndex;
  final int? selectedAnswer;
  final bool isLoading;
  final String? error;
  final Duration timeElapsed;
  final Duration? timeRemaining;

  QuizSessionState({
    this.quiz,
    this.session,
    required this.currentQuestionIndex,
    this.selectedAnswer,
    required this.isLoading,
    this.error,
    required this.timeElapsed,
    this.timeRemaining,
  });

  QuizSessionState copyWith({
    Quiz? quiz,
    QuizSession? session,
    int? currentQuestionIndex,
    int? selectedAnswer,
    bool? isLoading,
    String? error,
    Duration? timeElapsed,
    Duration? timeRemaining,
  }) {
    return QuizSessionState(
      quiz: quiz ?? this.quiz,
      session: session ?? this.session,
      currentQuestionIndex: currentQuestionIndex ?? this.currentQuestionIndex,
      selectedAnswer: selectedAnswer,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      timeElapsed: timeElapsed ?? this.timeElapsed,
      timeRemaining: timeRemaining ?? this.timeRemaining,
    );
  }
}

class QuizSessionNotifier extends StateNotifier<QuizSessionState> {
  final String quizId;
  final QuizService _quizService = QuizService();
  Timer? _timer;

  QuizSessionNotifier(this.quizId)
      : super(QuizSessionState(
          currentQuestionIndex: 0,
          isLoading: true,
          timeElapsed: Duration.zero,
        )) {
    _initializeQuiz();
  }

  Future<void> _initializeQuiz() async {
    try {
      final session = await _quizService.startQuiz(quizId);
      state = state.copyWith(
        quiz: session.quiz,
        session: session,
        isLoading: false,
        timeRemaining: session.quiz.timeLimit > 0 
            ? Duration(minutes: session.quiz.timeLimit)
            : null,
      );
      _startTimer();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      final newTimeElapsed = state.timeElapsed + const Duration(seconds: 1);
      Duration? newTimeRemaining = state.timeRemaining;
      
      if (newTimeRemaining != null) {
        newTimeRemaining = newTimeRemaining - const Duration(seconds: 1);
        if (newTimeRemaining.inSeconds <= 0) {
          _timer?.cancel();
          _finishQuiz();
          return;
        }
      }

      state = state.copyWith(
        timeElapsed: newTimeElapsed,
        timeRemaining: newTimeRemaining,
      );
    });
  }

  void selectAnswer(int answerIndex) {
    state = state.copyWith(selectedAnswer: answerIndex);
  }

  Future<void> submitAnswer() async {
    if (state.selectedAnswer == null || state.session == null) return;

    try {
      await _quizService.submitAnswer(state.selectedAnswer!);
      
      if (state.session!.isCompleted) {
        _finishQuiz();
      } else {
        state = state.copyWith(
          currentQuestionIndex: state.currentQuestionIndex + 1,
          selectedAnswer: null,
        );
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> _finishQuiz() async {
    try {
      _timer?.cancel();
      final result = await _quizService.completeQuiz();
      // Navigation will be handled by the UI
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}

class QuizScreen extends ConsumerWidget {
  final String quizId;

  const QuizScreen({super.key, required this.quizId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final sessionState = ref.watch(quizSessionProvider(quizId));

    if (sessionState.isLoading) {
      return Scaffold(
        appBar: AppBar(title: Text(TempLocalizations.startQuiz)),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (sessionState.error != null) {
      return Scaffold(
        appBar: AppBar(title: Text(TempLocalizations.error)),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: AppConfig.paddingM),
              Text(
                sessionState.error!,
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConfig.paddingL),
              ElevatedButton(
                onPressed: () => context.pop(),
                child: Text(TempLocalizations.close),
              ),
            ],
          ),
        ),
      );
    }

    final quiz = sessionState.quiz!;
    final currentQuestion = quiz.questions[sessionState.currentQuestionIndex];

    return Scaffold(
      appBar: AppBar(
        title: Text(quiz.title),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => _showExitDialog(context, ref),
        ),
        actions: [
          if (sessionState.timeRemaining != null)
            Padding(
              padding: const EdgeInsets.all(AppConfig.paddingS),
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConfig.paddingM,
                    vertical: AppConfig.paddingS,
                  ),
                  decoration: BoxDecoration(
                    color: _getTimeColor(sessionState.timeRemaining!, theme),
                    borderRadius: BorderRadius.circular(AppConfig.radiusS),
                  ),
                  child: Text(
                    _formatDuration(sessionState.timeRemaining!),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // Progress Bar
          _buildProgressBar(context, theme, sessionState),
          
          // Question Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConfig.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Question Header
                  _buildQuestionHeader(context, theme, sessionState),
                  const SizedBox(height: AppConfig.paddingL),
                  
                  // Question Text
                  _buildQuestionText(context, theme, currentQuestion),
                  const SizedBox(height: AppConfig.paddingL),
                  
                  // Answer Options
                  _buildAnswerOptions(context, theme, currentQuestion, sessionState, ref),
                ],
              ),
            ),
          ),
          
          // Navigation Buttons
          _buildNavigationButtons(context, sessionState, ref),
        ],
      ),
    );
  }

  Widget _buildProgressBar(BuildContext context, ThemeData theme, QuizSessionState state) {
    final progress = (state.currentQuestionIndex + 1) / state.quiz!.questions.length;
    
    return Container(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Question ${state.currentQuestionIndex + 1} of ${state.quiz!.questions.length}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '${(progress * 100).toInt()}%',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConfig.paddingS),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: theme.colorScheme.surfaceVariant,
            valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionHeader(BuildContext context, ThemeData theme, QuizSessionState state) {
    final question = state.quiz!.questions[state.currentQuestionIndex];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingM),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(AppConfig.paddingS),
              decoration: BoxDecoration(
                color: _getDifficultyColor(question.difficulty),
                borderRadius: BorderRadius.circular(AppConfig.radiusS),
              ),
              child: Icon(
                _getDifficultyIcon(question.difficulty),
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: AppConfig.paddingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Question ${state.currentQuestionIndex + 1}',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    question.difficulty.name.toUpperCase(),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuestionText(BuildContext context, ThemeData theme, Question question) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Text(
          question.text,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            height: 1.4,
          ),
        ),
      ),
    );
  }

  Widget _buildAnswerOptions(
    BuildContext context,
    ThemeData theme,
    Question question,
    QuizSessionState state,
    WidgetRef ref,
  ) {
    return Column(
      children: question.options.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = state.selectedAnswer == index;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: AppConfig.paddingM),
          child: Card(
            elevation: isSelected ? AppConfig.elevationM : AppConfig.elevationS,
            child: InkWell(
              onTap: () {
                ref.read(quizSessionProvider(quizId).notifier).selectAnswer(index);
              },
              borderRadius: BorderRadius.circular(AppConfig.radiusM),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppConfig.paddingL),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppConfig.radiusM),
                  border: isSelected
                      ? Border.all(
                          color: theme.colorScheme.primary,
                          width: 2,
                        )
                      : null,
                  color: isSelected
                      ? theme.colorScheme.primaryContainer
                      : null,
                ),
                child: Row(
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isSelected
                            ? theme.colorScheme.primary
                            : theme.colorScheme.outline,
                      ),
                      child: Center(
                        child: Text(
                          String.fromCharCode(65 + index), // A, B, C, D
                          style: TextStyle(
                            color: isSelected
                                ? theme.colorScheme.onPrimary
                                : theme.colorScheme.onSurfaceVariant,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: AppConfig.paddingM),
                    Expanded(
                      child: Text(
                        option,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: isSelected
                              ? theme.colorScheme.onPrimaryContainer
                              : null,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildNavigationButtons(
    BuildContext context,
    QuizSessionState state,
    WidgetRef ref,
  ) {
    final isLastQuestion = state.currentQuestionIndex == state.quiz!.questions.length - 1;
    
    return Container(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: state.selectedAnswer != null
              ? () async {
                  await ref.read(quizSessionProvider(quizId).notifier).submitAnswer();
                  
                  // Check if quiz is completed
                  final session = ref.read(quizSessionProvider(quizId)).session;
                  if (session?.isCompleted == true) {
                    final result = await QuizService().completeQuiz();
                    if (context.mounted) {
                      context.pushReplacement('/results/${result.id}');
                    }
                  }
                }
              : null,
          child: Text(
            isLastQuestion ? TempLocalizations.finishQuiz : TempLocalizations.nextQuestion,
          ),
        ),
      ),
    );
  }

  Color _getTimeColor(Duration timeRemaining, ThemeData theme) {
    final minutes = timeRemaining.inMinutes;
    if (minutes <= 2) return AppConfig.errorColor;
    if (minutes <= 5) return AppConfig.warningColor;
    return theme.colorScheme.primary;
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  Color _getDifficultyColor(QuestionDifficulty difficulty) {
    switch (difficulty) {
      case QuestionDifficulty.easy:
        return AppConfig.successColor;
      case QuestionDifficulty.medium:
        return AppConfig.warningColor;
      case QuestionDifficulty.hard:
        return AppConfig.errorColor;
    }
  }

  IconData _getDifficultyIcon(QuestionDifficulty difficulty) {
    switch (difficulty) {
      case QuestionDifficulty.easy:
        return Icons.sentiment_satisfied;
      case QuestionDifficulty.medium:
        return Icons.sentiment_neutral;
      case QuestionDifficulty.hard:
        return Icons.sentiment_very_dissatisfied;
    }
  }

  void _showExitDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exit Quiz'),
        content: const Text('Are you sure you want to exit? Your progress will be lost.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(TempLocalizations.cancel),
          ),
          TextButton(
            onPressed: () {
              QuizService().abandonQuiz();
              Navigator.of(context).pop();
              context.pop();
            },
            child: const Text('Exit'),
          ),
        ],
      ),
    );
  }
}
