import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../config/app_config.dart';
import '../utils/temp_localizations.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  final _apiKeyController = TextEditingController();
  bool _isTestingConnection = false;

  @override
  void initState() {
    super.initState();
    // Pre-fill with the API key from config
    _apiKeyController.text = 'AIzaSyA0C1cNjFlLfKUB0ovHS84fyucjODjsvQI';
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(TempLocalizations.settings),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConfig.paddingM),
        children: [
          // API Configuration Section
          _buildSectionHeader(context, TempLocalizations.apiConfiguration, Icons.api),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConfig.paddingL),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    TempLocalizations.geminiApiKey,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: AppConfig.paddingS),
                  Text(
                    TempLocalizations.apiKeyDescription,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: AppConfig.paddingM),
                  
                  TextFormField(
                    controller: _apiKeyController,
                    decoration: InputDecoration(
                      labelText: TempLocalizations.geminiApiKey,
                      hintText: TempLocalizations.apiKeyPlaceholder,
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: AppConfig.paddingM),
                  
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isTestingConnection ? null : _testConnection,
                      child: _isTestingConnection
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : Text(TempLocalizations.testConnection),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: AppConfig.paddingL),

          // Appearance Section
          _buildSectionHeader(context, 'Appearance', Icons.palette),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.language),
                  title: Text(TempLocalizations.language),
                  subtitle: const Text('English'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _showLanguageDialog(context),
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.brightness_6),
                  title: Text(TempLocalizations.theme),
                  subtitle: const Text('System'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _showThemeDialog(context),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppConfig.paddingL),

          // About Section
          _buildSectionHeader(context, TempLocalizations.about, Icons.info),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.info),
                  title: Text(TempLocalizations.version),
                  subtitle: Text(AppConfig.appVersion),
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.person),
                  title: Text(TempLocalizations.developer),
                  subtitle: const Text('Augment Code'),
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.description),
                  title: Text(TempLocalizations.licenses),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _showLicensesDialog(context),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title, IconData icon) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConfig.paddingM),
      child: Row(
        children: [
          Icon(
            icon,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: AppConfig.paddingS),
          Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _testConnection() async {
    setState(() {
      _isTestingConnection = true;
    });

    try {
      final apiKey = _apiKeyController.text.trim();
      if (apiKey.isEmpty) {
        _showSnackBar('Please enter an API key first');
        return;
      }

      // Simulate API test
      await Future.delayed(const Duration(seconds: 2));
      
      _showSnackBar(TempLocalizations.connectionSuccessful, isSuccess: true);
    } catch (e) {
      _showSnackBar('${TempLocalizations.connectionFailed}: ${e.toString()}');
    } finally {
      setState(() {
        _isTestingConnection = false;
      });
    }
  }

  void _showLanguageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(TempLocalizations.language),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('English'),
              value: 'en',
              groupValue: 'en',
              onChanged: (value) => Navigator.of(context).pop(),
            ),
            RadioListTile<String>(
              title: const Text('العربية'),
              value: 'ar',
              groupValue: 'en',
              onChanged: (value) => Navigator.of(context).pop(),
            ),
          ],
        ),
      ),
    );
  }

  void _showThemeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(TempLocalizations.theme),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: Text(TempLocalizations.lightMode),
              value: 'light',
              groupValue: 'system',
              onChanged: (value) => Navigator.of(context).pop(),
            ),
            RadioListTile<String>(
              title: Text(TempLocalizations.darkMode),
              value: 'dark',
              groupValue: 'system',
              onChanged: (value) => Navigator.of(context).pop(),
            ),
            RadioListTile<String>(
              title: Text(TempLocalizations.systemMode),
              value: 'system',
              groupValue: 'system',
              onChanged: (value) => Navigator.of(context).pop(),
            ),
          ],
        ),
      ),
    );
  }

  void _showLicensesDialog(BuildContext context) {
    showLicensePage(context: context);
  }

  void _showSnackBar(String message, {bool isSuccess = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isSuccess ? AppConfig.successColor : null,
      ),
    );
  }
}
