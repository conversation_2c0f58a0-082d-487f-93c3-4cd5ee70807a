import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../config/app_config.dart';
import '../models/analytics_models.dart';
import '../services/advanced_analytics_service.dart';
import '../utils/temp_localizations.dart';

class KnowledgeMapScreen extends StatefulWidget {
  const KnowledgeMapScreen({super.key});

  @override
  State<KnowledgeMapScreen> createState() => _KnowledgeMapScreenState();
}

class _KnowledgeMapScreenState extends State<KnowledgeMapScreen>
    with TickerProviderStateMixin {
  final AdvancedAnalyticsService _analyticsService = AdvancedAnalyticsService();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  String? _selectedTopic;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _loadData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    await _analyticsService.initialize();
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(TempLocalizations.knowledgeMap),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(context),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : FadeTransition(
              opacity: _fadeAnimation,
              child: _buildKnowledgeMap(context, theme),
            ),
    );
  }

  Widget _buildKnowledgeMap(BuildContext context, ThemeData theme) {
    final knowledgeMap = _analyticsService.knowledgeMap;
    
    if (knowledgeMap == null || knowledgeMap.topics.isEmpty) {
      return _buildEmptyState(context, theme);
    }

    return Column(
      children: [
        _buildLegend(context, theme),
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppConfig.paddingM),
            child: Column(
              children: [
                _buildTopicsGrid(context, theme, knowledgeMap),
                if (_selectedTopic != null) ...[
                  const SizedBox(height: AppConfig.paddingL),
                  _buildTopicDetails(context, theme, knowledgeMap),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLegend(BuildContext context, ThemeData theme) {
    return Container(
      margin: const EdgeInsets.all(AppConfig.paddingM),
      padding: const EdgeInsets.all(AppConfig.paddingM),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(AppConfig.radiusM),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildLegendItem('ضعيف', _getKnowledgeColor(KnowledgeLevel.weak)),
          _buildLegendItem('متوسط', _getKnowledgeColor(KnowledgeLevel.average)),
          _buildLegendItem('جيد', _getKnowledgeColor(KnowledgeLevel.good)),
          _buildLegendItem('ممتاز', _getKnowledgeColor(KnowledgeLevel.excellent)),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildTopicsGrid(BuildContext context, ThemeData theme, KnowledgeMap knowledgeMap) {
    final topics = knowledgeMap.topics.values.toList();
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppConfig.paddingM,
        mainAxisSpacing: AppConfig.paddingM,
        childAspectRatio: 1.2,
      ),
      itemCount: topics.length,
      itemBuilder: (context, index) {
        final topic = topics[index];
        return _buildTopicCard(context, theme, topic, knowledgeMap);
      },
    );
  }

  Widget _buildTopicCard(BuildContext context, ThemeData theme, TopicKnowledge topic, KnowledgeMap knowledgeMap) {
    final isSelected = _selectedTopic == topic.topicName;
    final color = _getKnowledgeColor(topic.level);
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTopic = isSelected ? null : topic.topicName;
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          border: Border.all(
            color: isSelected ? color : color.withOpacity(0.3),
            width: isSelected ? 3 : 1,
          ),
          borderRadius: BorderRadius.circular(AppConfig.radiusM),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: color.withOpacity(0.3),
                    blurRadius: 8,
                    spreadRadius: 2,
                  ),
                ]
              : null,
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.paddingM),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Topic Icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getTopicIcon(topic.topicName),
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(height: AppConfig.paddingS),
              
              // Topic Name
              Text(
                topic.topicName,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppConfig.paddingXS),
              
              // Mastery Percentage
              Text(
                '${(topic.masteryLevel * 100).toInt()}%',
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              // Progress Bar
              const SizedBox(height: AppConfig.paddingXS),
              LinearProgressIndicator(
                value: topic.masteryLevel,
                backgroundColor: color.withOpacity(0.2),
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
              
              // Question Count
              const SizedBox(height: AppConfig.paddingXS),
              Text(
                '${topic.totalQuestions} سؤال',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopicDetails(BuildContext context, ThemeData theme, KnowledgeMap knowledgeMap) {
    final topic = knowledgeMap.topics[_selectedTopic!];
    if (topic == null) return const SizedBox.shrink();
    
    final relatedTopics = knowledgeMap.topicRelations[_selectedTopic!] ?? [];
    
    return Card(
      elevation: AppConfig.elevationM,
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  _getTopicIcon(topic.topicName),
                  color: _getKnowledgeColor(topic.level),
                  size: 24,
                ),
                const SizedBox(width: AppConfig.paddingS),
                Expanded(
                  child: Text(
                    topic.topicName,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    setState(() {
                      _selectedTopic = null;
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: AppConfig.paddingM),
            
            // Statistics
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'مستوى الإتقان',
                    '${(topic.masteryLevel * 100).toInt()}%',
                    Icons.trending_up,
                    _getKnowledgeColor(topic.level),
                  ),
                ),
                const SizedBox(width: AppConfig.paddingS),
                Expanded(
                  child: _buildStatCard(
                    'الإجابات الصحيحة',
                    '${topic.correctAnswers}/${topic.totalQuestions}',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConfig.paddingS),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'متوسط الوقت',
                    '${topic.averageTime.toInt()}ث',
                    Icons.timer,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: AppConfig.paddingS),
                Expanded(
                  child: _buildStatCard(
                    'آخر ممارسة',
                    _formatLastPracticed(topic.lastPracticed),
                    Icons.schedule,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            
            // Related Topics
            if (relatedTopics.isNotEmpty) ...[
              const SizedBox(height: AppConfig.paddingM),
              Text(
                'مواضيع مرتبطة',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppConfig.paddingS),
              Wrap(
                spacing: AppConfig.paddingS,
                children: relatedTopics.map((relatedTopic) {
                  final relatedTopicData = knowledgeMap.topics[relatedTopic];
                  return Chip(
                    label: Text(relatedTopic),
                    backgroundColor: relatedTopicData != null
                        ? _getKnowledgeColor(relatedTopicData.level).withOpacity(0.2)
                        : theme.colorScheme.surfaceVariant,
                    onDeleted: () {
                      setState(() {
                        _selectedTopic = relatedTopic;
                      });
                    },
                    deleteIcon: const Icon(Icons.arrow_forward, size: 16),
                  );
                }).toList(),
              ),
            ],
            
            // Action Buttons
            const SizedBox(height: AppConfig.paddingM),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _practiceMore(topic.topicName),
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('تدرب أكثر'),
                  ),
                ),
                const SizedBox(width: AppConfig.paddingS),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _viewHistory(topic.topicName),
                    icon: const Icon(Icons.history),
                    label: const Text('عرض التاريخ'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConfig.radiusS),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: AppConfig.paddingXS),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
              fontSize: 16,
            ),
          ),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.map_outlined,
            size: 80,
            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.5),
          ),
          const SizedBox(height: AppConfig.paddingM),
          Text(
            'لا توجد بيانات كافية',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppConfig.paddingS),
          Text(
            'قم بإجراء بعض الاختبارات لرؤية خريطة معرفتك',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConfig.paddingL),
          ElevatedButton.icon(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.quiz),
            label: const Text('ابدأ اختبار'),
          ),
        ],
      ),
    );
  }

  Color _getKnowledgeColor(KnowledgeLevel level) {
    switch (level) {
      case KnowledgeLevel.weak:
        return Colors.red;
      case KnowledgeLevel.average:
        return Colors.orange;
      case KnowledgeLevel.good:
        return Colors.lightGreen;
      case KnowledgeLevel.excellent:
        return Colors.green;
    }
  }

  IconData _getTopicIcon(String topicName) {
    // Simple icon mapping based on topic name
    final lowerTopic = topicName.toLowerCase();
    if (lowerTopic.contains('رياضيات') || lowerTopic.contains('math')) return Icons.calculate;
    if (lowerTopic.contains('علوم') || lowerTopic.contains('science')) return Icons.science;
    if (lowerTopic.contains('تاريخ') || lowerTopic.contains('history')) return Icons.history_edu;
    if (lowerTopic.contains('لغة') || lowerTopic.contains('language')) return Icons.language;
    if (lowerTopic.contains('جغرافيا') || lowerTopic.contains('geography')) return Icons.public;
    return Icons.book;
  }

  String _formatLastPracticed(DateTime lastPracticed) {
    final now = DateTime.now();
    final difference = now.difference(lastPracticed);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else {
      return '${difference.inMinutes} دقيقة';
    }
  }

  Future<void> _refreshData() async {
    setState(() {
      _isLoading = true;
    });
    await _analyticsService.refreshAnalytics();
    setState(() {
      _isLoading = false;
    });
  }

  void _practiceMore(String topicName) {
    // Navigate to practice screen for this topic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('سيتم إضافة تدريبات لموضوع: $topicName')),
    );
  }

  void _viewHistory(String topicName) {
    // Navigate to history screen filtered by topic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('سيتم عرض تاريخ: $topicName')),
    );
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('كيفية قراءة خريطة المعرفة'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('🔴 أحمر: مستوى ضعيف (0-40%)'),
              SizedBox(height: 8),
              Text('🟡 أصفر: مستوى متوسط (41-70%)'),
              SizedBox(height: 8),
              Text('🟢 أخضر فاتح: مستوى جيد (71-85%)'),
              SizedBox(height: 8),
              Text('🟢 أخضر داكن: مستوى ممتاز (86-100%)'),
              SizedBox(height: 16),
              Text(
                'انقر على أي موضوع لعرض التفاصيل والمواضيع المرتبطة به.',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }
}
