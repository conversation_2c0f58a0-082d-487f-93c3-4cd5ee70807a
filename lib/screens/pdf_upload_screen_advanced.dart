import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../config/app_config.dart';
import '../utils/temp_localizations.dart';
import '../services/pdf_service.dart';
import '../services/quiz_service.dart';
import '../models/settings_model.dart';
import '../app.dart';

class PdfUploadScreen extends ConsumerStatefulWidget {
  const PdfUploadScreen({super.key});

  @override
  ConsumerState<PdfUploadScreen> createState() => _PdfUploadScreenState();
}

class _PdfUploadScreenState extends ConsumerState<PdfUploadScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _questionCountController = TextEditingController(text: '10');

  late AnimationController _animationController;
  late AnimationController _uploadAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _scaleAnimation;

  bool _isLoading = false;
  String? _selectedPdfPath;
  Map<String, dynamic>? _pdfInfo;
  double _uploadProgress = 0.0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _uploadAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<double>(
      begin: 30.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _uploadAnimationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _questionCountController.dispose();
    _animationController.dispose();
    _uploadAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final settings = ref.watch(appSettingsProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(TempLocalizations.uploadPdf),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(context),
          ),
        ],
      ),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConfig.paddingM),
            physics: settings.reduceMotion 
                ? const NeverScrollableScrollPhysics()
                : const BouncingScrollPhysics(),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Transform.translate(
                    offset: Offset(0, _slideAnimation.value),
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: _buildWelcomeCard(context, theme),
                    ),
                  ),
                  const SizedBox(height: AppConfig.paddingL),
                  Transform.translate(
                    offset: Offset(0, _slideAnimation.value * 1.5),
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: _buildPdfSelectionCard(context, theme),
                    ),
                  ),
                  if (_selectedPdfPath != null) ...[
                    const SizedBox(height: AppConfig.paddingL),
                    Transform.translate(
                      offset: Offset(0, _slideAnimation.value * 2),
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: _buildPdfInfoCard(context, theme),
                      ),
                    ),
                    const SizedBox(height: AppConfig.paddingL),
                    Transform.translate(
                      offset: Offset(0, _slideAnimation.value * 2.5),
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: _buildQuizConfigCard(context, theme, settings),
                      ),
                    ),
                    const SizedBox(height: AppConfig.paddingL),
                    Transform.translate(
                      offset: Offset(0, _slideAnimation.value * 3),
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: _buildGenerateButton(context, theme),
                      ),
                    ),
                  ],
                  const SizedBox(height: AppConfig.paddingXL),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildWelcomeCard(BuildContext context, ThemeData theme) {
    return Card(
      elevation: AppConfig.elevationM,
      child: Container(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConfig.radiusM),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primary.withOpacity(0.1),
              theme.colorScheme.secondary.withOpacity(0.1),
            ],
          ),
        ),
        child: Column(
          children: [
            Icon(
              Icons.auto_awesome,
              size: 48,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(height: AppConfig.paddingM),
            Text(
              'إنشاء اختبار ذكي',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: AppConfig.paddingS),
            Text(
              'ارفع ملف PDF وسنقوم بإنشاء اختبار تفاعلي باستخدام الذكاء الاصطناعي',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPdfSelectionCard(BuildContext context, ThemeData theme) {
    return Card(
      elevation: AppConfig.elevationS,
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.picture_as_pdf,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: AppConfig.paddingS),
                Text(
                  'اختيار ملف PDF',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConfig.paddingM),
            InkWell(
              onTap: _isLoading ? null : _selectPdf,
              borderRadius: BorderRadius.circular(AppConfig.radiusM),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppConfig.paddingXL),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: _selectedPdfPath != null 
                        ? theme.colorScheme.primary
                        : theme.colorScheme.outline,
                    width: 2,
                    style: BorderStyle.solid,
                  ),
                  borderRadius: BorderRadius.circular(AppConfig.radiusM),
                  color: _selectedPdfPath != null
                      ? theme.colorScheme.primary.withOpacity(0.05)
                      : theme.colorScheme.surfaceVariant.withOpacity(0.3),
                ),
                child: Column(
                  children: [
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      child: _isLoading
                          ? const CircularProgressIndicator()
                          : Icon(
                              _selectedPdfPath != null 
                                  ? Icons.check_circle
                                  : Icons.cloud_upload_outlined,
                              size: 64,
                              color: _selectedPdfPath != null
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.onSurfaceVariant,
                              key: ValueKey(_selectedPdfPath != null),
                            ),
                    ),
                    const SizedBox(height: AppConfig.paddingM),
                    Text(
                      _selectedPdfPath != null 
                          ? 'تم اختيار الملف بنجاح'
                          : 'اضغط لاختيار ملف PDF',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: _selectedPdfPath != null
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurfaceVariant,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: AppConfig.paddingS),
                    Text(
                      _selectedPdfPath != null
                          ? 'يمكنك اختيار ملف آخر إذا أردت'
                          : 'ملفات PDF فقط • حد أقصى 50 ميجابايت',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPdfInfoCard(BuildContext context, ThemeData theme) {
    if (_pdfInfo == null) return const SizedBox.shrink();

    return ScaleTransition(
      scale: _scaleAnimation,
      child: Card(
        elevation: AppConfig.elevationS,
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.paddingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: AppConfig.paddingS),
                  Text(
                    'معلومات الملف',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppConfig.paddingM),
              _buildInfoRow(
                context,
                theme,
                Icons.description,
                'اسم الملف',
                _pdfInfo!['fileName'].toString(),
              ),
              _buildInfoRow(
                context,
                theme,
                Icons.pages,
                'عدد الصفحات',
                '${_pdfInfo!['pageCount']} صفحة',
              ),
              _buildInfoRow(
                context,
                theme,
                Icons.storage,
                'حجم الملف',
                '${(_pdfInfo!['fileSizeMB'] as double).toStringAsFixed(1)} ميجابايت',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    ThemeData theme,
    IconData icon,
    String label,
    String value,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppConfig.paddingXS),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: AppConfig.paddingS),
          Text(
            '$label: ',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuizConfigCard(BuildContext context, ThemeData theme, AppSettings settings) {
    return Card(
      elevation: AppConfig.elevationS,
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.tune,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: AppConfig.paddingS),
                Text(
                  'إعدادات الاختبار',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConfig.paddingM),

            // Quiz Title
            TextFormField(
              controller: _titleController,
              decoration: InputDecoration(
                labelText: 'عنوان الاختبار',
                hintText: 'أدخل عنوان الاختبار',
                prefixIcon: const Icon(Icons.title),
                border: const OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'عنوان الاختبار مطلوب';
                }
                return null;
              },
            ),
            const SizedBox(height: AppConfig.paddingM),

            // Quiz Description
            TextFormField(
              controller: _descriptionController,
              decoration: InputDecoration(
                labelText: 'وصف الاختبار (اختياري)',
                hintText: 'أدخل وصف مختصر للاختبار',
                prefixIcon: const Icon(Icons.description),
                border: const OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: AppConfig.paddingM),

            // Question Count
            TextFormField(
              controller: _questionCountController,
              decoration: InputDecoration(
                labelText: 'عدد الأسئلة',
                hintText: 'من 5 إلى 50 سؤال',
                prefixIcon: const Icon(Icons.quiz),
                border: const OutlineInputBorder(),
                suffixText: 'سؤال',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                final count = int.tryParse(value ?? '');
                if (count == null || count < 5 || count > 50) {
                  return 'يجب أن يكون بين 5-50 سؤال';
                }
                return null;
              },
            ),
            const SizedBox(height: AppConfig.paddingM),

            // AI Settings Preview
            Container(
              padding: const EdgeInsets.all(AppConfig.paddingM),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(AppConfig.radiusS),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.smart_toy,
                        size: 20,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: AppConfig.paddingS),
                      Text(
                        'إعدادات الذكاء الاصطناعي',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConfig.paddingS),
                  Text(
                    'مستوى التنوع: ${_getVarietyLevel(settings.aiTemperature)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Text(
                    'توليد التفسيرات: ${settings.generateExplanations ? "مفعل" : "معطل"}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenerateButton(BuildContext context, ThemeData theme) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : _generateQuiz,
        icon: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.auto_awesome),
        label: Text(
          _isLoading ? 'جاري إنشاء الاختبار...' : 'إنشاء الاختبار',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          elevation: AppConfig.elevationM,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.radiusM),
          ),
        ),
      ),
    );
  }

  String _getVarietyLevel(double temperature) {
    if (temperature <= 0.3) return 'محافظ';
    if (temperature <= 0.7) return 'متوازن';
    return 'إبداعي';
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('كيفية استخدام المولد'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('1. اختر ملف PDF يحتوي على محتوى تعليمي'),
              SizedBox(height: 8),
              Text('2. أدخل عنوان ووصف للاختبار'),
              SizedBox(height: 8),
              Text('3. حدد عدد الأسئلة المطلوبة'),
              SizedBox(height: 8),
              Text('4. اضغط على "إنشاء الاختبار"'),
              SizedBox(height: 16),
              Text(
                'نصائح:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• استخدم ملفات PDF واضحة ومقروءة'),
              Text('• تأكد من أن المحتوى تعليمي'),
              Text('• الحد الأقصى لحجم الملف 50 ميجابايت'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }

  Future<void> _selectPdf() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final pdfService = PdfService();
      final file = await pdfService.pickPdfFile();

      if (file != null) {
        print('📄 Selected PDF: ${file.path}');

        // Validate PDF
        final isValid = await pdfService.validatePdfFile(file);
        if (!isValid) {
          _showError('ملف PDF غير صالح. يرجى اختيار ملف PDF صحيح.');
          return;
        }
        print('✅ PDF validation passed');

        // Get PDF info
        final info = await pdfService.getPdfInfo(file);
        print('📊 PDF Info: ${info['pageCount']} pages, ${(info['fileSizeMB'] as double).toStringAsFixed(1)} MB');

        setState(() {
          _selectedPdfPath = file.path;
          _pdfInfo = info;
          final fileName = info['fileName'].toString();
          _titleController.text = fileName.replaceAll('.pdf', '').replaceAll('.PDF', '');
        });

        _uploadAnimationController.forward();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ تم تحميل الملف: ${info['pageCount']} صفحة'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        print('❌ No PDF file selected');
      }
    } catch (e) {
      print('❌ PDF selection error: $e');
      _showError('فشل في اختيار الملف: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _generateQuiz() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedPdfPath == null) {
      _showError('يرجى اختيار ملف PDF أولاً.');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      print('🚀 Starting quiz generation...');

      // Get question count from form
      final questionCount = int.tryParse(_questionCountController.text) ?? 10;

      // Create quiz creation request
      final request = QuizCreationRequest(
        pdfFile: File(_selectedPdfPath!),
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        questionCount: questionCount,
      );

      // Show progress dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري إنشاء الاختبار...'),
              SizedBox(height: 8),
              Text(
                'قد يستغرق هذا بضع دقائق',
                style: TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
      );

      // Generate quiz using real AI service
      final quizService = QuizService();
      final quiz = await quizService.createQuizFromPdf(request);

      print('✅ Quiz generated successfully: ${quiz.id}');

      if (mounted) {
        // Close progress dialog
        Navigator.of(context).pop();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('🎉 تم إنشاء الاختبار بـ ${quiz.questions.length} سؤال!'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to the generated quiz
        context.pushReplacement('/quiz/${quiz.id}');
      }
    } catch (e) {
      print('❌ Quiz generation failed: $e');

      if (mounted) {
        // Close progress dialog if open
        try {
          Navigator.of(context).pop();
        } catch (_) {}

        _showError('فشل في إنشاء الاختبار:\n${e.toString()}');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }
}
