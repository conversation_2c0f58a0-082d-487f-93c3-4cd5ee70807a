import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../config/app_config.dart';
import '../models/settings_model.dart';
import '../services/settings_service.dart';
import '../services/storage_service.dart';
import '../utils/app_localizations.dart';
import '../widgets/language_switcher.dart';

// Provider for settings service
final settingsServiceProvider = ChangeNotifierProvider<SettingsService>((ref) {
  return SettingsService();
});

// Provider for current settings
final settingsProvider = Provider<AppSettings>((ref) {
  final service = ref.watch(settingsServiceProvider);
  return service.settings;
});

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    
    // Initialize settings service
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(settingsServiceProvider).initialize();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final settings = ref.watch(settingsProvider);
    final settingsService = ref.read(settingsServiceProvider);



    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).settings),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(value, settingsService),
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    const Icon(Icons.download),
                    const SizedBox(width: 8),
                    Text(AppLocalizations.of(context).exportSettings),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'import',
                child: Row(
                  children: [
                    const Icon(Icons.upload),
                    const SizedBox(width: 8),
                    Text(AppLocalizations.of(context).importSettings),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'reset',
                child: Row(
                  children: [
                    const Icon(Icons.restore, color: Colors.red),
                    const SizedBox(width: 8),
                    Text(
                      AppLocalizations.of(context).resetSettings,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(icon: const Icon(Icons.palette), text: AppLocalizations.of(context).appearance),
            Tab(icon: const Icon(Icons.quiz), text: AppLocalizations.of(context).quiz),
            Tab(icon: const Icon(Icons.picture_as_pdf), text: 'PDF'),
            Tab(icon: const Icon(Icons.notifications), text: AppLocalizations.of(context).notifications),
            Tab(icon: const Icon(Icons.more_horiz), text: AppLocalizations.of(context).more),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAppearanceTab(context, theme, settings, settingsService),
          _buildQuizTab(context, theme, settings, settingsService),
          _buildPdfTab(context, theme, settings, settingsService),
          _buildNotificationsTab(context, theme, settings, settingsService),
          _buildMoreTab(context, theme, settings, settingsService),
        ],
      ),
    );
  }

  Widget _buildAppearanceTab(
    BuildContext context,
    ThemeData theme,
    AppSettings settings,
    SettingsService service,
  ) {
    return ListView(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      children: [
        _buildSectionCard(
          title: AppLocalizations.of(context).theme,
          icon: Icons.brightness_6,
          children: [
            _buildDropdownTile<AppThemeMode>(
              title: AppLocalizations.of(context).themeMode,
              value: settings.themeMode,
              items: AppThemeMode.values,
              itemBuilder: (mode) => _getThemeModeText(mode),
              onChanged: service.setThemeMode,
            ),
          ],
        ),
        const SizedBox(height: AppConfig.paddingM),
        _buildSectionCard(
          title: AppLocalizations.of(context).language,
          icon: Icons.language,
          children: [
            const LanguageDropdown(),
            _buildSwitchTile(
              title: 'تفعيل RTL',
              subtitle: 'تفعيل الكتابة من اليمين لليسار',
              value: settings.enableRTL,
              onChanged: service.setEnableRTL,
            ),
          ],
        ),
        const SizedBox(height: AppConfig.paddingM),
        _buildSectionCard(
          title: 'التخصيص',
          icon: Icons.tune,
          children: [
            _buildSliderTile(
              title: 'حجم الخط',
              subtitle: '${settings.fontSize.toInt()} نقطة',
              value: settings.fontSize,
              min: 12.0,
              max: 24.0,
              divisions: 12,
              onChanged: service.setFontSize,
            ),
            _buildSwitchTile(
              title: 'تفعيل الحركات',
              subtitle: 'تفعيل الحركات والانتقالات المتحركة',
              value: settings.enableAnimations,
              onChanged: service.setEnableAnimations,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuizTab(
    BuildContext context,
    ThemeData theme,
    AppSettings settings,
    SettingsService service,
  ) {
    return ListView(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      children: [
        _buildSectionCard(
          title: 'إعدادات الاختبار الافتراضية',
          icon: Icons.quiz,
          children: [
            _buildSliderTile(
              title: 'عدد الأسئلة الافتراضي',
              subtitle: '${settings.defaultQuestionCount} سؤال',
              value: settings.defaultQuestionCount.toDouble(),
              min: 5.0,
              max: 50.0,
              divisions: 9,
              onChanged: (value) => service.setDefaultQuestionCount(value.toInt()),
            ),
            _buildSliderTile(
              title: 'الوقت المحدد (دقيقة)',
              subtitle: settings.defaultTimeLimit == 0 ? 'بلا حدود' : '${settings.defaultTimeLimit} دقيقة',
              value: settings.defaultTimeLimit.toDouble(),
              min: 0.0,
              max: 120.0,
              divisions: 12,
              onChanged: (value) => service.setDefaultTimeLimit(value.toInt()),
            ),
            _buildDropdownTile<QuizDifficulty>(
              title: 'مستوى الصعوبة الافتراضي',
              value: settings.defaultDifficulty,
              items: QuizDifficulty.values,
              itemBuilder: (difficulty) => _getDifficultyText(difficulty),
              onChanged: service.setDefaultDifficulty,
            ),
          ],
        ),
        const SizedBox(height: AppConfig.paddingM),
        _buildSectionCard(
          title: 'سلوك الاختبار',
          icon: Icons.settings,
          children: [
            _buildSwitchTile(
              title: 'إرسال تلقائي للإجابات',
              subtitle: 'إرسال الإجابة تلقائياً بعد الاختيار',
              value: settings.autoSubmitAnswers,
              onChanged: service.setAutoSubmitAnswers,
            ),
            _buildSwitchTile(
              title: 'إظهار الإجابات الصحيحة',
              subtitle: 'إظهار الإجابة الصحيحة بعد الإجابة',
              value: settings.showCorrectAnswers,
              onChanged: service.setShowCorrectAnswers,
            ),
            _buildSwitchTile(
              title: 'تفعيل التلميحات',
              subtitle: 'إظهار تلميحات مساعدة أثناء الاختبار',
              value: settings.enableHints,
              onChanged: service.setEnableHints,
            ),
            _buildSwitchTile(
              title: 'خلط الأسئلة',
              subtitle: 'عرض الأسئلة بترتيب عشوائي',
              value: settings.shuffleQuestions,
              onChanged: service.setShuffleQuestions,
            ),
            _buildSwitchTile(
              title: 'خلط الخيارات',
              subtitle: 'عرض خيارات الإجابة بترتيب عشوائي',
              value: settings.shuffleAnswers,
              onChanged: service.setShuffleAnswers,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPdfTab(
    BuildContext context,
    ThemeData theme,
    AppSettings settings,
    SettingsService service,
  ) {
    return ListView(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      children: [
        _buildSectionCard(
          title: 'معالجة PDF',
          icon: Icons.picture_as_pdf,
          children: [
            _buildSliderTile(
              title: 'الحد الأقصى لحجم PDF (MB)',
              subtitle: '${settings.maxPdfSizeMB} ميجابايت',
              value: settings.maxPdfSizeMB.toDouble(),
              min: 10.0,
              max: 200.0,
              divisions: 19,
              onChanged: (value) => service.setMaxPdfSizeMB(value.toInt()),
            ),
            _buildSwitchTile(
              title: 'استخراج الصور',
              subtitle: 'استخراج الصور من PDF لتحليل أفضل',
              value: settings.extractImages,
              onChanged: service.setExtractImages,
            ),
            _buildSwitchTile(
              title: 'معالجة المعادلات',
              subtitle: 'تحليل المعادلات الرياضية والعلمية',
              value: settings.processFormulas,
              onChanged: service.setProcessFormulas,
            ),
            _buildSwitchTile(
              title: 'تحسين جودة النص',
              subtitle: 'تحسين جودة النص المستخرج من PDF',
              value: settings.enhanceTextQuality,
              onChanged: service.setEnhanceTextQuality,
            ),
          ],
        ),
      ],
    );
  }



  Widget _buildNotificationsTab(
    BuildContext context,
    ThemeData theme,
    AppSettings settings,
    SettingsService service,
  ) {
    return ListView(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      children: [
        _buildSectionCard(
          title: 'إعدادات الإشعارات',
          icon: Icons.notifications,
          children: [
            _buildSwitchTile(
              title: 'تفعيل الإشعارات',
              subtitle: 'تلقي إشعارات من التطبيق',
              value: settings.enableNotifications,
              onChanged: service.setEnableNotifications,
            ),
            _buildDropdownTile<NotificationFrequency>(
              title: 'تكرار التذكيرات',
              value: settings.reminderFrequency,
              items: NotificationFrequency.values,
              itemBuilder: (frequency) => _getFrequencyText(frequency),
              onChanged: service.setReminderFrequency,
              enabled: settings.enableNotifications,
            ),
            _buildSwitchTile(
              title: 'تفعيل الصوت',
              subtitle: 'تشغيل صوت مع الإشعارات',
              value: settings.soundEnabled,
              onChanged: service.setSoundEnabled,
              enabled: settings.enableNotifications,
            ),
            _buildSwitchTile(
              title: 'تفعيل الاهتزاز',
              subtitle: 'اهتزاز الجهاز مع الإشعارات',
              value: settings.vibrationEnabled,
              onChanged: service.setVibrationEnabled,
              enabled: settings.enableNotifications,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMoreTab(
    BuildContext context,
    ThemeData theme,
    AppSettings settings,
    SettingsService service,
  ) {
    return ListView(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      children: [
        _buildSectionCard(
          title: 'الخصوصية والأمان',
          icon: Icons.security,
          children: [
            _buildSwitchTile(
              title: 'حفظ تاريخ الاختبارات',
              subtitle: 'حفظ نتائج الاختبارات على الجهاز محلياً',
              value: settings.saveQuizHistory,
              onChanged: service.setSaveQuizHistory,
            ),
            _buildSwitchTile(
              title: 'حفظ ملفات PDF',
              subtitle: 'الاحتفاظ بنسخة من ملفات PDF المرفوعة',
              value: settings.enableCaching,
              onChanged: service.setEnableCaching,
            ),
            _buildSwitchTile(
              title: 'مسح البيانات عند الخروج',
              subtitle: 'حذف البيانات المؤقتة عند إغلاق التطبيق',
              value: !settings.saveQuizHistory,
              onChanged: (value) => service.setSaveQuizHistory(!value),
            ),
          ],
        ),
        const SizedBox(height: AppConfig.paddingM),
        _buildSectionCard(
          title: 'البيانات والتحليلات',
          icon: Icons.analytics,
          children: [
            _buildSwitchTile(
              title: 'مشاركة التحليلات',
              subtitle: 'مشاركة بيانات الاستخدام المجهولة لتحسين التطبيق',
              value: settings.shareAnalytics,
              onChanged: service.setShareAnalytics,
            ),
            _buildSwitchTile(
              title: 'تقارير الأخطاء',
              subtitle: 'إرسال تقارير الأخطاء تلقائياً للمطورين',
              value: settings.enableCrashReporting,
              onChanged: service.setEnableCrashReporting,
            ),
            _buildSwitchTile(
              title: 'النسخ الاحتياطي السحابي',
              subtitle: 'نسخ احتياطي للإعدادات في السحابة',
              value: settings.autoBackup,
              onChanged: service.setAutoBackup,
            ),
            ListTile(
              leading: const Icon(Icons.delete_forever, color: Colors.red),
              title: const Text(
                'مسح جميع البيانات',
                style: TextStyle(color: Colors.red),
              ),
              subtitle: const Text('حذف جميع الاختبارات والنتائج والإعدادات'),
              onTap: () => _showClearDataDialog(service),
            ),
          ],
        ),
        const SizedBox(height: AppConfig.paddingM),
        _buildSectionCard(
          title: 'الأداء والتخزين',
          icon: Icons.storage,
          children: [
            _buildSwitchTile(
              title: 'تفعيل التخزين المؤقت',
              subtitle: 'تخزين البيانات مؤقتاً لتحسين الأداء',
              value: settings.enableCaching,
              onChanged: service.setEnableCaching,
            ),
            _buildSliderTile(
              title: 'حجم التخزين المؤقت',
              subtitle: '${settings.cacheSize} ميجابايت',
              value: settings.cacheSize.toDouble(),
              min: 50.0,
              max: 500.0,
              divisions: 9,
              onChanged: (value) => service.setCacheSize(value.toInt()),
              enabled: settings.enableCaching,
            ),
            _buildSwitchTile(
              title: 'تحميل المحتوى مسبقاً',
              subtitle: 'تحميل المحتوى في الخلفية',
              value: settings.preloadContent,
              onChanged: service.setPreloadContent,
            ),
            _buildSwitchTile(
              title: 'تحسين الصور',
              subtitle: 'ضغط الصور لتوفير المساحة',
              value: settings.optimizeImages,
              onChanged: service.setOptimizeImages,
            ),
          ],
        ),
        const SizedBox(height: AppConfig.paddingM),
        _buildSectionCard(
          title: 'إمكانية الوصول',
          icon: Icons.accessibility,
          children: [
            _buildSwitchTile(
              title: 'قارئ الشاشة',
              subtitle: 'دعم قارئ الشاشة للمكفوفين',
              value: settings.enableScreenReader,
              onChanged: service.setEnableScreenReader,
            ),
            _buildSwitchTile(
              title: 'التباين العالي',
              subtitle: 'زيادة التباين لسهولة القراءة',
              value: settings.highContrast,
              onChanged: service.setHighContrast,
            ),
            _buildSwitchTile(
              title: 'النص الكبير',
              subtitle: 'استخدام خط أكبر',
              value: settings.largeText,
              onChanged: service.setLargeText,
            ),
            _buildSwitchTile(
              title: 'تقليل الحركة',
              subtitle: 'تقليل الحركات والانتقالات',
              value: settings.reduceMotion,
              onChanged: service.setReduceMotion,
            ),
          ],
        ),
        const SizedBox(height: AppConfig.paddingM),
        _buildSectionCard(
          title: 'معلومات التطبيق',
          icon: Icons.info,
          children: [
            ListTile(
              leading: const Icon(Icons.info),
              title: Text(AppLocalizations.of(context).version),
              subtitle: Text(AppConfig.appVersion),
            ),
            ListTile(
              leading: const Icon(Icons.person),
              title: Text(AppLocalizations.of(context).developer),
              subtitle: const Text('DR/Ah7med'),
            ),
            ListTile(
              leading: const Icon(Icons.description),
              title: Text(AppLocalizations.of(context).licenses),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showLicensesDialog(context),
            ),
          ],
        ),
      ],
    );
  }

  // Helper Widgets
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: Icon(icon),
            title: Text(
              title,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
    bool enabled = true,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: enabled ? onChanged : null,
    );
  }

  Widget _buildSliderTile({
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required Function(double) onChanged,
    int? divisions,
    bool enabled = true,
  }) {
    return ListTile(
      title: Text(title),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(subtitle),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: enabled ? onChanged : null,
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownTile<T>({
    required String title,
    required T value,
    required List<T> items,
    required String Function(T) itemBuilder,
    required Function(T) onChanged,
    bool enabled = true,
  }) {
    return ListTile(
      title: Text(title),
      subtitle: DropdownButton<T>(
        value: value,
        isExpanded: true,
        onChanged: enabled ? (T? newValue) {
          if (newValue != null) onChanged(newValue);
        } : null,
        items: items.map<DropdownMenuItem<T>>((T item) {
          return DropdownMenuItem<T>(
            value: item,
            child: Text(itemBuilder(item)),
          );
        }).toList(),
      ),
    );
  }

  // Helper Methods
  String _getThemeModeText(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.system:
        return 'تلقائي (حسب النظام)';
      case AppThemeMode.light:
        return 'فاتح';
      case AppThemeMode.dark:
        return 'داكن';
    }
  }

  String _getLanguageText(Language language) {
    switch (language) {
      case Language.arabic:
        return 'العربية';
      case Language.english:
        return 'English';
    }
  }

  String _getDifficultyText(QuizDifficulty difficulty) {
    switch (difficulty) {
      case QuizDifficulty.easy:
        return 'سهل';
      case QuizDifficulty.medium:
        return 'متوسط';
      case QuizDifficulty.hard:
        return 'صعب';
      case QuizDifficulty.mixed:
        return 'مختلط';
    }
  }

  String _getFrequencyText(NotificationFrequency frequency) {
    switch (frequency) {
      case NotificationFrequency.never:
        return 'أبداً';
      case NotificationFrequency.daily:
        return 'يومياً';
      case NotificationFrequency.weekly:
        return 'أسبوعياً';
      case NotificationFrequency.monthly:
        return 'شهرياً';
    }
  }

  String _getTemperatureDescription(double temperature) {
    if (temperature <= 0.3) return 'محافظ';
    if (temperature <= 0.7) return 'متوازن';
    return 'إبداعي';
  }

  // Action Methods

  void _handleMenuAction(String action, SettingsService service) {
    switch (action) {
      case 'export':
        _exportSettings(service);
        break;
      case 'import':
        _importSettings(service);
        break;
      case 'reset':
        _showResetDialog(service);
        break;
    }
  }

  void _exportSettings(SettingsService service) {
    final settingsJson = service.exportSettings();
    // Here you would typically save to file or share
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تصدير الإعدادات')),
    );
  }

  void _importSettings(SettingsService service) {
    // Here you would typically pick a file and import
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('استيراد الإعدادات غير متاح حالياً')),
    );
  }

  void _showResetDialog(SettingsService service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الإعدادات'),
        content: const Text('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await service.resetToDefaults();
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تم إعادة تعيين الإعدادات')),
                );
              }
            },
            child: const Text('إعادة تعيين', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showLicensesDialog(BuildContext context) {
    showLicensePage(
      context: context,
      applicationName: AppConfig.appName,
      applicationVersion: AppConfig.appVersion,
    );
  }

  void _showClearDataDialog(SettingsService service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.warning,
              color: Colors.red,
            ),
            const SizedBox(width: AppConfig.paddingS),
            const Text('تحذير'),
          ],
        ),
        content: const Text(
          'هل أنت متأكد من حذف جميع البيانات؟\n\n'
          'سيتم حذف:\n'
          '• جميع الاختبارات المحفوظة\n'
          '• جميع النتائج\n'
          '• جميع الإعدادات\n'
          '• ملفات PDF المحفوظة\n\n'
          'لا يمكن التراجع عن هذا الإجراء!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();

              // Show loading dialog
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const AlertDialog(
                  content: Row(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: AppConfig.paddingM),
                      Text('جاري حذف البيانات...'),
                    ],
                  ),
                ),
              );

              try {
                // Clear all data
                await service.clearAllData();

                // Clear storage service data
                final storageService = StorageService();
                await storageService.initialize();
                // Add method to clear all storage data

                if (mounted) {
                  Navigator.of(context).pop(); // Close loading dialog
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف جميع البيانات بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  Navigator.of(context).pop(); // Close loading dialog
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في حذف البيانات: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text(
              'حذف جميع البيانات',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
