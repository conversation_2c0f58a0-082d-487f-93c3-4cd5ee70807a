import 'dart:io';
import 'package:flutter/material.dart';
import '../config/app_config.dart';
import '../services/pdf_service.dart';
import '../services/quiz_service.dart';

// Extension methods for PDF upload screen
extension PdfUploadMethods on State {
  Widget buildPdfInfoCard(BuildContext context, ThemeData theme, Map<String, dynamic>? pdfInfo) {
    if (pdfInfo == null) return const SizedBox.shrink();

    return Card(
      elevation: AppConfig.elevationS,
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: AppConfig.paddingS),
                Text(
                  'معلومات الملف',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConfig.paddingM),
            buildInfoRow(
              context,
              theme,
              Icons.description,
              'اسم الملف',
              pdfInfo['fileName'].toString(),
            ),
            buildInfoRow(
              context,
              theme,
              Icons.pages,
              'عدد الصفحات',
              '${pdfInfo['pageCount']} صفحة',
            ),
            buildInfoRow(
              context,
              theme,
              Icons.storage,
              'حجم الملف',
              '${(pdfInfo['fileSizeMB'] as double).toStringAsFixed(1)} ميجابايت',
            ),
          ],
        ),
      ),
    );
  }

  Widget buildInfoRow(
    BuildContext context,
    ThemeData theme,
    IconData icon,
    String label,
    String value,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppConfig.paddingXS),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: AppConfig.paddingS),
          Text(
            '$label: ',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildGenerateButton(
    BuildContext context, 
    ThemeData theme, 
    bool isLoading, 
    VoidCallback? onPressed,
  ) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton.icon(
        onPressed: isLoading ? null : onPressed,
        icon: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.auto_awesome),
        label: Text(
          isLoading ? 'جاري إنشاء الاختبار...' : 'إنشاء الاختبار',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          elevation: AppConfig.elevationM,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.radiusM),
          ),
        ),
      ),
    );
  }

  String getVarietyLevel(double temperature) {
    if (temperature <= 0.3) return 'محافظ';
    if (temperature <= 0.7) return 'متوازن';
    return 'إبداعي';
  }

  void showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('كيفية استخدام المولد'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('1. اختر ملف PDF يحتوي على محتوى تعليمي'),
              SizedBox(height: 8),
              Text('2. أدخل عنوان ووصف للاختبار'),
              SizedBox(height: 8),
              Text('3. حدد عدد الأسئلة المطلوبة'),
              SizedBox(height: 8),
              Text('4. اضغط على "إنشاء الاختبار"'),
              SizedBox(height: 16),
              Text(
                'نصائح:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• استخدم ملفات PDF واضحة ومقروءة'),
              Text('• تأكد من أن المحتوى تعليمي'),
              Text('• الحد الأقصى لحجم الملف 50 ميجابايت'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }

  Future<void> selectPdf(
    BuildContext context,
    Function(bool) setLoading,
    Function(String?) setSelectedPdfPath,
    Function(Map<String, dynamic>?) setPdfInfo,
    TextEditingController titleController,
  ) async {
    try {
      setLoading(true);

      final pdfService = PdfService();
      final file = await pdfService.pickPdfFile();

      if (file != null) {
        print('📄 Selected PDF: ${file.path}');

        // Validate PDF
        final isValid = await pdfService.validatePdfFile(file);
        if (!isValid) {
          showError(context, 'ملف PDF غير صالح. يرجى اختيار ملف PDF صحيح.');
          return;
        }
        print('✅ PDF validation passed');

        // Get PDF info
        final info = await pdfService.getPdfInfo(file);
        print('📊 PDF Info: ${info['pageCount']} pages, ${(info['fileSizeMB'] as double).toStringAsFixed(1)} MB');

        setSelectedPdfPath(file.path);
        setPdfInfo(info);
        
        final fileName = info['fileName'].toString();
        titleController.text = fileName.replaceAll('.pdf', '').replaceAll('.PDF', '');

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ تم تحميل الملف: ${info['pageCount']} صفحة'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        print('❌ No PDF file selected');
      }
    } catch (e) {
      print('❌ PDF selection error: $e');
      showError(context, 'فشل في اختيار الملف: ${e.toString()}');
    } finally {
      setLoading(false);
    }
  }

  Future<void> generateQuiz(
    BuildContext context,
    GlobalKey<FormState> formKey,
    String? selectedPdfPath,
    TextEditingController titleController,
    TextEditingController descriptionController,
    TextEditingController questionCountController,
    Function(bool) setLoading,
    Function(BuildContext) navigateToQuiz,
  ) async {
    if (!formKey.currentState!.validate()) return;

    if (selectedPdfPath == null) {
      showError(context, 'يرجى اختيار ملف PDF أولاً.');
      return;
    }

    setLoading(true);

    try {
      print('🚀 Starting quiz generation...');

      // Get question count from form
      final questionCount = int.tryParse(questionCountController.text) ?? 10;

      // Create quiz creation request
      final request = QuizCreationRequest(
        pdfFile: File(selectedPdfPath),
        title: titleController.text.trim(),
        description: descriptionController.text.trim(),
        questionCount: questionCount,
      );

      // Show progress dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري إنشاء الاختبار...'),
              SizedBox(height: 8),
              Text(
                'قد يستغرق هذا بضع دقائق',
                style: TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
      );

      // Generate quiz using real AI service
      final quizService = QuizService();
      final quiz = await quizService.createQuizFromPdf(request);

      print('✅ Quiz generated successfully: ${quiz.id}');

      if (context.mounted) {
        // Close progress dialog
        Navigator.of(context).pop();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('🎉 تم إنشاء الاختبار بـ ${quiz.questions.length} سؤال!'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to the generated quiz
        navigateToQuiz(context);
      }
    } catch (e) {
      print('❌ Quiz generation failed: $e');

      if (context.mounted) {
        // Close progress dialog if open
        try {
          Navigator.of(context).pop();
        } catch (_) {}

        showError(context, 'فشل في إنشاء الاختبار:\n${e.toString()}');
      }
    } finally {
      setLoading(false);
    }
  }

  void showError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }
}
