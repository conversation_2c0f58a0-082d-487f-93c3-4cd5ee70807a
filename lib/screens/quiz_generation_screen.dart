import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import '../config/app_config.dart';
import '../services/quiz_service.dart';
import '../models/quiz_model.dart';
import '../flutter_gen/gen_l10n/app_localizations.dart';

// Provider for quiz generation state
final quizGenerationProvider = StateNotifierProvider.autoDispose
    .family<QuizGenerationNotifier, QuizGenerationState, Map<String, dynamic>>(
  (ref, params) => QuizGenerationNotifier(params),
);

enum QuizGenerationStatus {
  idle,
  extractingText,
  generatingQuestions,
  saving,
  completed,
  error,
}

class QuizGenerationState {
  final QuizGenerationStatus status;
  final String? message;
  final double progress;
  final Quiz? quiz;
  final String? error;

  QuizGenerationState({
    required this.status,
    this.message,
    required this.progress,
    this.quiz,
    this.error,
  });

  QuizGenerationState copyWith({
    QuizGenerationStatus? status,
    String? message,
    double? progress,
    Quiz? quiz,
    String? error,
  }) {
    return QuizGenerationState(
      status: status ?? this.status,
      message: message ?? this.message,
      progress: progress ?? this.progress,
      quiz: quiz ?? this.quiz,
      error: error ?? this.error,
    );
  }
}

class QuizGenerationNotifier extends StateNotifier<QuizGenerationState> {
  final Map<String, dynamic> params;
  final QuizService _quizService = QuizService();

  QuizGenerationNotifier(this.params)
      : super(QuizGenerationState(
          status: QuizGenerationStatus.idle,
          progress: 0.0,
        )) {
    _generateQuiz();
  }

  Future<void> _generateQuiz() async {
    try {
      final pdfPath = params['pdfPath'] as String;
      final title = params['title'] as String;
      final description = params['description'] as String;
      final questionCount = params['questionCount'] as int;

      // Step 1: Extract text from PDF
      state = state.copyWith(
        status: QuizGenerationStatus.extractingText,
        message: 'Extracting text from PDF...',
        progress: 0.2,
      );

      await Future.delayed(const Duration(seconds: 1)); // Simulate processing

      // Step 2: Generate questions
      state = state.copyWith(
        status: QuizGenerationStatus.generatingQuestions,
        message: 'Generating quiz questions...',
        progress: 0.6,
      );

      await Future.delayed(const Duration(seconds: 2)); // Simulate AI processing

      // Step 3: Save quiz
      state = state.copyWith(
        status: QuizGenerationStatus.saving,
        message: 'Saving quiz...',
        progress: 0.9,
      );

      final request = QuizCreationRequest(
        pdfFile: File(pdfPath),
        title: title,
        description: description,
        questionCount: questionCount,
      );

      final quiz = await _quizService.createQuizFromPdf(request);

      // Step 4: Complete
      state = state.copyWith(
        status: QuizGenerationStatus.completed,
        message: 'Quiz generated successfully!',
        progress: 1.0,
        quiz: quiz,
      );

    } catch (e) {
      state = state.copyWith(
        status: QuizGenerationStatus.error,
        error: e.toString(),
        progress: 0.0,
      );
    }
  }

  void retry() {
    state = QuizGenerationState(
      status: QuizGenerationStatus.idle,
      progress: 0.0,
    );
    _generateQuiz();
  }
}

class QuizGenerationScreen extends ConsumerWidget {
  final String pdfPath;
  final String title;
  final String description;
  final int questionCount;

  const QuizGenerationScreen({
    super.key,
    required this.pdfPath,
    required this.title,
    required this.description,
    required this.questionCount,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    
    final params = {
      'pdfPath': pdfPath,
      'title': title,
      'description': description,
      'questionCount': questionCount,
    };
    
    final generationState = ref.watch(quizGenerationProvider(params));

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.generatingQuiz),
        leading: generationState.status == QuizGenerationStatus.completed ||
                generationState.status == QuizGenerationStatus.error
            ? IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => context.pop(),
              )
            : null,
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          children: [
            // Header
            _buildHeader(context, l10n, theme),
            const SizedBox(height: AppConfig.paddingXL),
            
            // Progress Section
            Expanded(
              child: _buildProgressSection(context, l10n, theme, generationState, ref, params),
            ),
            
            // Action Buttons
            _buildActionButtons(context, l10n, generationState, ref, params),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, AppLocalizations l10n, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  size: 32,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: AppConfig.paddingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'AI Quiz Generation',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Creating your personalized quiz',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConfig.paddingM),
            Divider(color: theme.colorScheme.outline),
            const SizedBox(height: AppConfig.paddingS),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Title',
                    title,
                    Icons.title,
                  ),
                ),
                const SizedBox(width: AppConfig.paddingM),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Questions',
                    '$questionCount',
                    Icons.quiz,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(BuildContext context, String label, String value, IconData icon) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: theme.colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: AppConfig.paddingS),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildProgressSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    QuizGenerationState state,
    WidgetRef ref,
    Map<String, dynamic> params,
  ) {
    switch (state.status) {
      case QuizGenerationStatus.idle:
      case QuizGenerationStatus.extractingText:
      case QuizGenerationStatus.generatingQuestions:
      case QuizGenerationStatus.saving:
        return _buildLoadingState(context, l10n, theme, state);
        
      case QuizGenerationStatus.completed:
        return _buildSuccessState(context, l10n, theme, state);
        
      case QuizGenerationStatus.error:
        return _buildErrorState(context, l10n, theme, state, ref, params);
    }
  }

  Widget _buildLoadingState(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    QuizGenerationState state,
  ) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Animated Loading Indicator
        SpinKitWave(
          color: theme.colorScheme.primary,
          size: 50.0,
        ),
        const SizedBox(height: AppConfig.paddingXL),
        
        // Progress Bar
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConfig.paddingL),
            child: Column(
              children: [
                LinearProgressIndicator(
                  value: state.progress,
                  backgroundColor: theme.colorScheme.surfaceVariant,
                  valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                ),
                const SizedBox(height: AppConfig.paddingM),
                Text(
                  state.message ?? l10n.processing,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppConfig.paddingS),
                Text(
                  '${(state.progress * 100).toInt()}% complete',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: AppConfig.paddingL),
        
        // Processing Steps
        _buildProcessingSteps(context, theme, state),
      ],
    );
  }

  Widget _buildProcessingSteps(BuildContext context, ThemeData theme, QuizGenerationState state) {
    final steps = [
      ('Extracting text from PDF', QuizGenerationStatus.extractingText),
      ('Generating questions with AI', QuizGenerationStatus.generatingQuestions),
      ('Saving quiz', QuizGenerationStatus.saving),
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingM),
        child: Column(
          children: steps.map((step) {
            final isActive = state.status == step.$2;
            final isCompleted = steps.indexOf(step) < steps.indexWhere((s) => s.$2 == state.status);
            
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: AppConfig.paddingS),
              child: Row(
                children: [
                  Icon(
                    isCompleted ? Icons.check_circle : 
                    isActive ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                    color: isCompleted ? AppConfig.successColor :
                           isActive ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: AppConfig.paddingM),
                  Expanded(
                    child: Text(
                      step.$1,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                        color: isCompleted ? AppConfig.successColor :
                               isActive ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildSuccessState(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    QuizGenerationState state,
  ) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.check_circle,
          size: 80,
          color: AppConfig.successColor,
        ),
        const SizedBox(height: AppConfig.paddingL),
        Text(
          l10n.successQuizGenerated,
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppConfig.successColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppConfig.paddingM),
        if (state.quiz != null) ...[
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConfig.paddingL),
              child: Column(
                children: [
                  Text(
                    state.quiz!.title,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppConfig.paddingS),
                  Text(
                    '${state.quiz!.questions.length} questions generated',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildErrorState(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    QuizGenerationState state,
    WidgetRef ref,
    Map<String, dynamic> params,
  ) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.error_outline,
          size: 80,
          color: theme.colorScheme.error,
        ),
        const SizedBox(height: AppConfig.paddingL),
        Text(
          'Generation Failed',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.error,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppConfig.paddingM),
        Card(
          color: theme.colorScheme.errorContainer,
          child: Padding(
            padding: const EdgeInsets.all(AppConfig.paddingL),
            child: Text(
              state.error ?? 'An unexpected error occurred',
              style: TextStyle(
                color: theme.colorScheme.onErrorContainer,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    AppLocalizations l10n,
    QuizGenerationState state,
    WidgetRef ref,
    Map<String, dynamic> params,
  ) {
    switch (state.status) {
      case QuizGenerationStatus.completed:
        return Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  if (state.quiz != null) {
                    context.pushReplacement('/quiz/${state.quiz!.id}');
                  }
                },
                child: Text(l10n.startQuiz),
              ),
            ),
            const SizedBox(height: AppConfig.paddingS),
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => context.go('/'),
                child: Text('Go to Home'),
              ),
            ),
          ],
        );
        
      case QuizGenerationStatus.error:
        return Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  ref.read(quizGenerationProvider(params).notifier).retry();
                },
                child: Text(l10n.retry),
              ),
            ),
            const SizedBox(height: AppConfig.paddingS),
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => context.pop(),
                child: Text(l10n.cancel),
              ),
            ),
          ],
        );
        
      default:
        return const SizedBox.shrink();
    }
  }
}
