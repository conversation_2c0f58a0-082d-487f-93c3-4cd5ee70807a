import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../config/app_config.dart';
import '../utils/temp_localizations.dart';
import '../services/storage_service.dart';

// Export the real screens
export 'quiz_screen_real.dart' show QuizScreen;
export 'results_screen_real.dart' show ResultsScreen;

// Quiz Generation Screen
class QuizGenerationScreen extends ConsumerWidget {
  final String pdfPath;
  final String title;
  final String description;
  final int questionCount;

  const QuizGenerationScreen({
    super.key,
    required this.pdfPath,
    required this.title,
    required this.description,
    required this.questionCount,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(TempLocalizations.generatingQuiz),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: AppConfig.paddingL),
            Text(
              TempLocalizations.generatingQuiz,
              style: theme.textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConfig.paddingM),
            Text(
              'Creating $questionCount questions from "$title"',
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}



// Results Screen is now exported from results_screen_real.dart

// History Screen - Real implementation
class HistoryScreen extends ConsumerWidget {
  const HistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(TempLocalizations.history),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: FutureBuilder(
        future: _loadQuizzes(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: theme.colorScheme.error,
                  ),
                  const SizedBox(height: AppConfig.paddingM),
                  Text(
                    'Error loading quizzes',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: AppConfig.paddingS),
                  Text(
                    snapshot.error.toString(),
                    style: theme.textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          final quizzes = snapshot.data as List<dynamic>? ?? [];

          if (quizzes.isEmpty) {
            return Padding(
              padding: const EdgeInsets.all(AppConfig.paddingM),
              child: Column(
                children: [
                  Expanded(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.history,
                            size: 80,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(height: AppConfig.paddingL),
                          Text(
                            TempLocalizations.noQuizzesYet,
                            style: theme.textTheme.titleLarge,
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: AppConfig.paddingS),
                          Text(
                            TempLocalizations.createFirstQuiz,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: AppConfig.paddingL),
                          ElevatedButton(
                            onPressed: () => context.push('/upload'),
                            child: Text(TempLocalizations.createQuiz),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(AppConfig.paddingM),
            itemCount: quizzes.length,
            itemBuilder: (context, index) {
              final quiz = quizzes[index];
              return Card(
                margin: const EdgeInsets.only(bottom: AppConfig.paddingM),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: theme.colorScheme.primaryContainer,
                    child: Icon(
                      Icons.quiz,
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                  ),
                  title: Text(
                    quiz['title'] ?? 'Quiz',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  subtitle: Text(
                    '${quiz['questionCount'] ?? 0} questions • ${quiz['status'] ?? 'ready'}',
                    style: theme.textTheme.bodySmall,
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.play_arrow),
                    onPressed: () => context.push('/quiz/${quiz['id']}'),
                  ),
                  onTap: () => context.push('/quiz/${quiz['id']}'),
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.push('/upload'),
        child: const Icon(Icons.add),
      ),
    );
  }

  Future<List<Map<String, dynamic>>> _loadQuizzes() async {
    try {
      // Try to load real quizzes from storage
      final storageService = StorageService();
      await storageService.initialize();
      final quizzes = await storageService.getAllQuizzes();

      return quizzes.map((quiz) => {
        'id': quiz.id,
        'title': quiz.title,
        'questionCount': quiz.questions.length,
        'status': quiz.status.name,
        'createdAt': quiz.createdAt.toString(),
      }).toList();
    } catch (e) {
      // Return empty list if storage fails
      return [];
    }
  }
}
