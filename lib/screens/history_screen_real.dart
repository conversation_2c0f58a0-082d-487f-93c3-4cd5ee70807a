import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../config/app_config.dart';
import '../services/storage_service.dart';
import '../models/quiz_model.dart';
import '../models/quiz_result_model.dart';
import '../utils/temp_localizations.dart';

// Provider for all quizzes
final allQuizzesProvider = FutureProvider<List<Quiz>>((ref) async {
  try {
    final storageService = StorageService();
    await storageService.initialize();
    return await storageService.getAllQuizzes();
  } catch (e) {
    return <Quiz>[];
  }
});

// Provider for all quiz results
final allResultsProvider = FutureProvider<List<QuizResult>>((ref) async {
  try {
    final storageService = StorageService();
    await storageService.initialize();
    return await storageService.getAllQuizResults();
  } catch (e) {
    return <QuizResult>[];
  }
});

class HistoryScreen extends ConsumerWidget {
  const HistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final quizzesAsync = ref.watch(allQuizzesProvider);
    final resultsAsync = ref.watch(allResultsProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(TempLocalizations.history),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(allQuizzesProvider);
              ref.invalidate(allResultsProvider);
            },
          ),
        ],
      ),
      body: DefaultTabController(
        length: 2,
        child: Column(
          children: [
            TabBar(
              tabs: [
                Tab(
                  icon: const Icon(Icons.quiz),
                  text: TempLocalizations.quizzes,
                ),
                Tab(
                  icon: const Icon(Icons.history),
                  text: TempLocalizations.results,
                ),
              ],
            ),
            Expanded(
              child: TabBarView(
                children: [
                  // Quizzes Tab
                  quizzesAsync.when(
                    data: (quizzes) => _buildQuizzesTab(context, theme, quizzes),
                    loading: () => const Center(child: CircularProgressIndicator()),
                    error: (error, stack) => _buildErrorWidget(context, theme, error.toString()),
                  ),
                  // Results Tab
                  resultsAsync.when(
                    data: (results) => _buildResultsTab(context, theme, results),
                    loading: () => const Center(child: CircularProgressIndicator()),
                    error: (error, stack) => _buildErrorWidget(context, theme, error.toString()),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuizzesTab(BuildContext context, ThemeData theme, List<Quiz> quizzes) {
    if (quizzes.isEmpty) {
      return _buildEmptyState(
        context,
        theme,
        Icons.quiz,
        'No quizzes found',
        'Create your first quiz by uploading a PDF',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      itemCount: quizzes.length,
      itemBuilder: (context, index) {
        final quiz = quizzes[index];
        return _buildQuizCard(context, theme, quiz);
      },
    );
  }

  Widget _buildResultsTab(BuildContext context, ThemeData theme, List<QuizResult> results) {
    if (results.isEmpty) {
      return _buildEmptyState(
        context,
        theme,
        Icons.history,
        'No quiz results found',
        'Complete a quiz to see your results here',
      );
    }

    // Sort results by completion date (newest first)
    final sortedResults = List<QuizResult>.from(results)
      ..sort((a, b) => (b.completedAt ?? DateTime.now()).compareTo(a.completedAt ?? DateTime.now()));

    return ListView.builder(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      itemCount: sortedResults.length,
      itemBuilder: (context, index) {
        final result = sortedResults[index];
        return _buildResultCard(context, theme, result);
      },
    );
  }

  Widget _buildQuizCard(BuildContext context, ThemeData theme, Quiz quiz) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConfig.paddingM),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: theme.colorScheme.primary,
          child: Text(
            quiz.questions.length.toString(),
            style: TextStyle(
              color: theme.colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          quiz.title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(quiz.description),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.picture_as_pdf,
                  size: 16,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    quiz.sourceFileName,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'start':
                context.push('/quiz/${quiz.id}');
                break;
              case 'delete':
                _showDeleteQuizDialog(context, quiz);
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'start',
              child: Row(
                children: [
                  const Icon(Icons.play_arrow),
                  const SizedBox(width: 8),
                  Text(TempLocalizations.startQuiz),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  const Icon(Icons.delete, color: Colors.red),
                  const SizedBox(width: 8),
                  Text(
                    TempLocalizations.delete,
                    style: const TextStyle(color: Colors.red),
                  ),
                ],
              ),
            ),
          ],
        ),
        onTap: () => context.push('/quiz/${quiz.id}'),
      ),
    );
  }

  Widget _buildResultCard(BuildContext context, ThemeData theme, QuizResult result) {
    final scoreColor = result.scorePercentage >= 70
        ? AppConfig.successColor
        : result.scorePercentage >= 50
            ? Colors.orange
            : AppConfig.errorColor;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConfig.paddingM),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: scoreColor,
          child: Text(
            '${result.scorePercentage.toInt()}%',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
        title: Text(
          result.quizTitle,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${result.questionResults.where((q) => q.isCorrect).length}/${result.questionResults.length} correct',
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  _formatDuration(Duration(seconds: result.totalTimeSeconds)),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  _formatDate(result.completedAt ?? result.startedAt),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'view':
                context.push('/results/${result.id}');
                break;
              case 'retake':
                context.push('/quiz/${result.quizId}');
                break;
              case 'delete':
                _showDeleteResultDialog(context, result);
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'view',
              child: Row(
                children: [
                  const Icon(Icons.visibility),
                  const SizedBox(width: 8),
                  Text(TempLocalizations.viewResults),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'retake',
              child: Row(
                children: [
                  const Icon(Icons.refresh),
                  const SizedBox(width: 8),
                  Text(TempLocalizations.retakeQuiz),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  const Icon(Icons.delete, color: Colors.red),
                  const SizedBox(width: 8),
                  Text(
                    TempLocalizations.delete,
                    style: const TextStyle(color: Colors.red),
                  ),
                ],
              ),
            ),
          ],
        ),
        onTap: () => context.push('/results/${result.id}'),
      ),
    );
  }

  Widget _buildEmptyState(
    BuildContext context,
    ThemeData theme,
    IconData icon,
    String title,
    String subtitle,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: AppConfig.paddingM),
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppConfig.paddingS),
          Text(
            subtitle,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConfig.paddingL),
          ElevatedButton(
            onPressed: () => context.push('/upload'),
            child: Text(TempLocalizations.uploadPdf),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context, ThemeData theme, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: theme.colorScheme.error,
          ),
          const SizedBox(height: AppConfig.paddingM),
          Text(
            'Error loading data',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          const SizedBox(height: AppConfig.paddingS),
          Text(
            error,
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConfig.paddingL),
          ElevatedButton(
            onPressed: () => context.go('/'),
            child: const Text('Go Home'),
          ),
        ],
      ),
    );
  }

  void _showDeleteQuizDialog(BuildContext context, Quiz quiz) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(TempLocalizations.deleteQuiz),
        content: Text('Are you sure you want to delete "${quiz.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(TempLocalizations.cancel),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                final storageService = StorageService();
                await storageService.initialize();
                await storageService.deleteQuiz(quiz.id);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Quiz "${quiz.title}" deleted')),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error deleting quiz: $e')),
                  );
                }
              }
            },
            child: Text(
              TempLocalizations.delete,
              style: const TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteResultDialog(BuildContext context, QuizResult result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Result'),
        content: Text('Are you sure you want to delete this result for "${result.quizTitle}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(TempLocalizations.cancel),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                final storageService = StorageService();
                await storageService.initialize();
                await storageService.deleteQuizResult(result.id);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Result deleted')),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error deleting result: $e')),
                  );
                }
              }
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes}m ${seconds}s';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
