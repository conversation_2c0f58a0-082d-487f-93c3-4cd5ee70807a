import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../config/app_config.dart';
import '../utils/temp_localizations.dart';
import '../services/pdf_service.dart';
import '../services/quiz_service.dart';
import '../models/question_model.dart';

class PdfUploadScreen extends ConsumerStatefulWidget {
  const PdfUploadScreen({super.key});

  @override
  ConsumerState<PdfUploadScreen> createState() => _PdfUploadScreenState();
}

class _PdfUploadScreenState extends ConsumerState<PdfUploadScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  bool _isLoading = false;
  String? _selectedPdfPath;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(TempLocalizations.uploadPdf),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConfig.paddingM),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // PDF Selection Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConfig.paddingL),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        TempLocalizations.selectPdf,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppConfig.paddingM),
                      
                      if (_selectedPdfPath == null) ...[
                        InkWell(
                          onTap: _selectPdf,
                          borderRadius: BorderRadius.circular(AppConfig.radiusM),
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(AppConfig.paddingXL),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: theme.colorScheme.outline,
                                style: BorderStyle.solid,
                                width: 2,
                              ),
                              borderRadius: BorderRadius.circular(AppConfig.radiusM),
                            ),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.cloud_upload_outlined,
                                  size: 64,
                                  color: theme.colorScheme.primary,
                                ),
                                const SizedBox(height: AppConfig.paddingM),
                                Text(
                                  TempLocalizations.selectPdf,
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    color: theme.colorScheme.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: AppConfig.paddingS),
                                Text(
                                  'Tap to select a PDF file',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: theme.colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ] else ...[
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(AppConfig.paddingM),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primaryContainer,
                            borderRadius: BorderRadius.circular(AppConfig.radiusM),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.picture_as_pdf,
                                color: theme.colorScheme.onPrimaryContainer,
                              ),
                              const SizedBox(width: AppConfig.paddingS),
                              Expanded(
                                child: Text(
                                  _selectedPdfPath!.split('/').last,
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    color: theme.colorScheme.onPrimaryContainer,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              IconButton(
                                icon: Icon(
                                  Icons.close,
                                  color: theme.colorScheme.onPrimaryContainer,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _selectedPdfPath = null;
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              
              if (_selectedPdfPath != null) ...[
                const SizedBox(height: AppConfig.paddingL),
                
                // Quiz Configuration Section
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(AppConfig.paddingL),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Quiz Configuration',
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: AppConfig.paddingM),
                        
                        // Quiz Title
                        TextFormField(
                          controller: _titleController,
                          decoration: InputDecoration(
                            labelText: TempLocalizations.quizTitle,
                            hintText: 'Enter quiz title',
                          ),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Quiz title is required';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: AppConfig.paddingM),
                        
                        // Quiz Description
                        TextFormField(
                          controller: _descriptionController,
                          decoration: InputDecoration(
                            labelText: TempLocalizations.quizDescription,
                            hintText: 'Enter quiz description (optional)',
                          ),
                          maxLines: 3,
                        ),
                        const SizedBox(height: AppConfig.paddingM),
                        
                        // Question Count
                        TextFormField(
                          initialValue: '10',
                          decoration: InputDecoration(
                            labelText: TempLocalizations.questionCount,
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            final count = int.tryParse(value ?? '');
                            if (count == null || count < 5 || count > 50) {
                              return 'Must be between 5-50';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: AppConfig.paddingL),
                
                // Generate Quiz Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _generateQuiz,
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(TempLocalizations.generateQuiz),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectPdf() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final pdfService = PdfService();
      final file = await pdfService.pickPdfFile();

      if (file != null) {
        print('📄 Selected PDF: ${file.path}');

        // Validate PDF
        final isValid = await pdfService.validatePdfFile(file);
        if (!isValid) {
          _showError('Invalid PDF file. Please select a valid PDF document.');
          return;
        }
        print('✅ PDF validation passed');

        // Get PDF info
        final info = await pdfService.getPdfInfo(file);
        print('📊 PDF Info: ${info['pageCount']} pages, ${(info['fileSizeMB'] as double).toStringAsFixed(1)} MB');

        setState(() {
          _selectedPdfPath = file.path;
          final fileName = info['fileName'].toString();
          _titleController.text = fileName.replaceAll('.pdf', '').replaceAll('.PDF', '');
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ PDF loaded: ${info['pageCount']} pages'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        print('❌ No PDF file selected');
      }
    } catch (e) {
      print('❌ PDF selection error: $e');
      _showError('Failed to select PDF: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _generateQuiz() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedPdfPath == null) {
      _showError('Please select a PDF file first.');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      print('🚀 Starting quiz generation...');

      // Get question count from form
      final questionCountText = _formKey.currentState!.value['questionCount'] ?? '10';
      final questionCount = int.tryParse(questionCountText) ?? 10;

      // Create quiz creation request
      final request = QuizCreationRequest(
        pdfFile: File(_selectedPdfPath!),
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        questionCount: questionCount,
      );

      print('📝 Request: ${request.title} - ${request.questionCount} questions');

      // Show progress dialog
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text('🤖 AI is analyzing your PDF...'),
                const SizedBox(height: 8),
                Text('This may take 30-60 seconds'),
              ],
            ),
          ),
        );
      }

      // Generate quiz using real AI service
      final quizService = QuizService();
      final quiz = await quizService.createQuizFromPdf(request);

      print('✅ Quiz generated successfully: ${quiz.id}');

      if (mounted) {
        // Close progress dialog
        Navigator.of(context).pop();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('🎉 Quiz created with ${quiz.questions.length} questions!'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to the generated quiz
        context.pushReplacement('/quiz/${quiz.id}');
      }
    } catch (e) {
      print('❌ Quiz generation failed: $e');

      if (mounted) {
        // Close progress dialog if open
        try {
          Navigator.of(context).pop();
        } catch (_) {}

        _showError('Quiz generation failed:\n${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
