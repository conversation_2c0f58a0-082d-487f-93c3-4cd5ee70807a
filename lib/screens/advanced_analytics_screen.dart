import 'package:flutter/material.dart';
import '../config/app_config.dart';
import '../models/analytics_models.dart';
import '../services/advanced_analytics_service.dart';
import '../utils/temp_localizations.dart';
import 'knowledge_map_screen.dart';
import 'learning_curves_screen.dart';
import 'error_analysis_screen.dart';
import 'performance_predictions_screen.dart';

class AdvancedAnalyticsScreen extends StatefulWidget {
  const AdvancedAnalyticsScreen({super.key});

  @override
  State<AdvancedAnalyticsScreen> createState() => _AdvancedAnalyticsScreenState();
}

class _AdvancedAnalyticsScreenState extends State<AdvancedAnalyticsScreen>
    with TickerProviderStateMixin {
  final AdvancedAnalyticsService _analyticsService = AdvancedAnalyticsService();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _loadData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    await _analyticsService.initialize();
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('التحليلات المتقدمة'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshAllData,
          ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showInfoDialog(context),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : FadeTransition(
              opacity: _fadeAnimation,
              child: _buildContent(context, theme),
            ),
    );
  }

  Widget _buildContent(BuildContext context, ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(context, theme),
          const SizedBox(height: AppConfig.paddingL),
          _buildQuickStats(context, theme),
          const SizedBox(height: AppConfig.paddingL),
          _buildAnalyticsGrid(context, theme),
          const SizedBox(height: AppConfig.paddingL),
          _buildRecentInsights(context, theme),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard(BuildContext context, ThemeData theme) {
    return Card(
      elevation: AppConfig.elevationM,
      child: Container(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConfig.radiusM),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primary.withOpacity(0.1),
              theme.colorScheme.secondary.withOpacity(0.1),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppConfig.paddingM),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.analytics,
                    color: theme.colorScheme.primary,
                    size: 32,
                  ),
                ),
                const SizedBox(width: AppConfig.paddingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مرحباً بك في التحليلات المتقدمة',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppConfig.paddingXS),
                      Text(
                        'اكتشف أنماط تعلمك وحسن أداءك',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConfig.paddingM),
            Text(
              'استخدم الذكاء الاصطناعي لتحليل أدائك وتوقع نتائجك المستقبلية. احصل على رؤى عميقة حول نقاط قوتك وضعفك.',
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context, ThemeData theme) {
    final knowledgeMap = _analyticsService.knowledgeMap;
    final learningCurve = _analyticsService.learningCurve;
    final errorPatterns = _analyticsService.errorPatterns;
    final predictions = _analyticsService.performancePredictions;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'المواضيع المتقنة',
            knowledgeMap?.topics.values
                .where((t) => t.level == KnowledgeLevel.excellent)
                .length.toString() ?? '0',
            Icons.star,
            Colors.green,
            theme,
          ),
        ),
        const SizedBox(width: AppConfig.paddingS),
        Expanded(
          child: _buildStatCard(
            'نقاط البيانات',
            learningCurve?.dataPoints.length.toString() ?? '0',
            Icons.show_chart,
            Colors.blue,
            theme,
          ),
        ),
        const SizedBox(width: AppConfig.paddingS),
        Expanded(
          child: _buildStatCard(
            'أنماط الأخطاء',
            errorPatterns?.length.toString() ?? '0',
            Icons.psychology,
            Colors.orange,
            theme,
          ),
        ),
        const SizedBox(width: AppConfig.paddingS),
        Expanded(
          child: _buildStatCard(
            'التنبؤات',
            predictions?.length.toString() ?? '0',
            Icons.auto_awesome,
            Colors.purple,
            theme,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingM),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: AppConfig.paddingXS),
            Text(
              value,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: theme.textTheme.bodySmall,
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticsGrid(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أدوات التحليل',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConfig.paddingM),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: AppConfig.paddingM,
          mainAxisSpacing: AppConfig.paddingM,
          childAspectRatio: 1.1,
          children: [
            _buildAnalyticsCard(
              'خريطة المعرفة',
              'اكتشف مستوى إتقانك في كل موضوع',
              Icons.map,
              Colors.green,
              () => Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const KnowledgeMapScreen()),
              ),
            ),
            _buildAnalyticsCard(
              'منحنيات التعلم',
              'تتبع تقدمك عبر الزمن',
              Icons.show_chart,
              Colors.blue,
              () => Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const LearningCurvesScreen()),
              ),
            ),
            _buildAnalyticsCard(
              'تحليل الأخطاء',
              'فهم أنماط أخطائك الشائعة',
              Icons.psychology,
              Colors.orange,
              () => Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const ErrorAnalysisScreen()),
              ),
            ),
            _buildAnalyticsCard(
              'تنبؤات الأداء',
              'توقع نتائجك المستقبلية',
              Icons.auto_awesome,
              Colors.purple,
              () => Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const PerformancePredictionsScreen()),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAnalyticsCard(String title, String description, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      elevation: AppConfig.elevationS,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConfig.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.paddingM),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(AppConfig.paddingM),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 32,
                ),
              ),
              const SizedBox(height: AppConfig.paddingM),
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConfig.paddingS),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentInsights(BuildContext context, ThemeData theme) {
    final insights = _generateRecentInsights();
    
    if (insights.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'رؤى حديثة',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConfig.paddingM),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConfig.paddingL),
            child: Column(
              children: insights.map((insight) => Padding(
                padding: const EdgeInsets.only(bottom: AppConfig.paddingM),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      insight['icon'],
                      color: insight['color'],
                      size: 20,
                    ),
                    const SizedBox(width: AppConfig.paddingS),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            insight['title'],
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            insight['description'],
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )).toList(),
            ),
          ),
        ),
      ],
    );
  }

  List<Map<String, dynamic>> _generateRecentInsights() {
    final insights = <Map<String, dynamic>>[];
    
    final knowledgeMap = _analyticsService.knowledgeMap;
    final learningCurve = _analyticsService.learningCurve;
    final errorPatterns = _analyticsService.errorPatterns;
    final predictions = _analyticsService.performancePredictions;

    // Knowledge Map Insights
    if (knowledgeMap != null && knowledgeMap.topics.isNotEmpty) {
      final excellentTopics = knowledgeMap.topics.values
          .where((t) => t.level == KnowledgeLevel.excellent)
          .length;
      
      if (excellentTopics > 0) {
        insights.add({
          'icon': Icons.star,
          'color': Colors.green,
          'title': 'إتقان ممتاز',
          'description': 'لديك إتقان ممتاز في $excellentTopics موضوع',
        });
      }
      
      final weakTopics = knowledgeMap.topics.values
          .where((t) => t.level == KnowledgeLevel.weak)
          .toList();
      
      if (weakTopics.isNotEmpty) {
        insights.add({
          'icon': Icons.warning,
          'color': Colors.orange,
          'title': 'يحتاج تحسين',
          'description': '${weakTopics.length} موضوع يحتاج لمزيد من التركيز',
        });
      }
    }

    // Learning Curve Insights
    if (learningCurve != null && learningCurve.dataPoints.isNotEmpty) {
      switch (learningCurve.trend) {
        case LearningTrend.improving:
          insights.add({
            'icon': Icons.trending_up,
            'color': Colors.green,
            'title': 'تحسن مستمر',
            'description': 'أداؤك يتحسن بشكل مستمر، استمر!',
          });
          break;
        case LearningTrend.declining:
          insights.add({
            'icon': Icons.trending_down,
            'color': Colors.red,
            'title': 'تراجع في الأداء',
            'description': 'يحتاج لمزيد من التركيز والممارسة',
          });
          break;
        default:
          break;
      }
    }

    // Error Patterns Insights
    if (errorPatterns != null && errorPatterns.isNotEmpty) {
      final mostCommonError = errorPatterns.reduce((a, b) => a.frequency > b.frequency ? a : b);
      insights.add({
        'icon': Icons.psychology,
        'color': Colors.orange,
        'title': 'نمط خطأ شائع',
        'description': 'أكثر أخطائك شيوعاً في ${mostCommonError.topicName}',
      });
    }

    // Predictions Insights
    if (predictions != null && predictions.isNotEmpty) {
      final avgPrediction = predictions.map((p) => p.predictedScore).reduce((a, b) => a + b) / predictions.length;
      if (avgPrediction >= 80) {
        insights.add({
          'icon': Icons.auto_awesome,
          'color': Colors.purple,
          'title': 'توقعات إيجابية',
          'description': 'النتائج المتوقعة ممتازة (${avgPrediction.toInt()}%)',
        });
      }
    }

    return insights.take(3).toList();
  }

  void _showInfoDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حول التحليلات المتقدمة'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('🗺️ خريطة المعرفة: تُظهر مستوى إتقانك في كل موضوع بألوان مختلفة'),
              SizedBox(height: 8),
              Text('📈 منحنيات التعلم: تتبع تقدمك وأداءك عبر الزمن'),
              SizedBox(height: 8),
              Text('🧠 تحليل الأخطاء: يحدد أنماط أخطائك الشائعة ويقترح حلول'),
              SizedBox(height: 8),
              Text('🔮 تنبؤات الأداء: يتوقع نتائجك المستقبلية بناءً على أدائك الحالي'),
              SizedBox(height: 16),
              Text(
                'جميع هذه التحليلات تستخدم الذكاء الاصطناعي لمساعدتك في تحسين أدائك.',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }

  Future<void> _refreshAllData() async {
    setState(() {
      _isLoading = true;
    });
    await _analyticsService.refreshAnalytics();
    setState(() {
      _isLoading = false;
    });
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تحديث جميع التحليلات بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
