import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../config/app_config.dart';
import '../models/analytics_models.dart';
import '../services/advanced_analytics_service.dart';

class PerformancePredictionsScreen extends StatefulWidget {
  const PerformancePredictionsScreen({super.key});

  @override
  State<PerformancePredictionsScreen> createState() => _PerformancePredictionsScreenState();
}

class _PerformancePredictionsScreenState extends State<PerformancePredictionsScreen>
    with TickerProviderStateMixin {
  final AdvancedAnalyticsService _analyticsService = AdvancedAnalyticsService();
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  bool _isLoading = true;
  String _selectedTimeframe = '7'; // 7, 14, 30 days

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );
    _loadData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    await _analyticsService.initialize();
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('تنبؤات الأداء'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.schedule),
            onSelected: (value) {
              setState(() {
                _selectedTimeframe = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: '7', child: Text('الأسبوع القادم')),
              const PopupMenuItem(value: '14', child: Text('الأسبوعين القادمين')),
              const PopupMenuItem(value: '30', child: Text('الشهر القادم')),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ScaleTransition(
              scale: _scaleAnimation,
              child: _buildContent(context, theme),
            ),
    );
  }

  Widget _buildContent(BuildContext context, ThemeData theme) {
    final predictions = _analyticsService.performancePredictions;
    
    if (predictions == null || predictions.isEmpty) {
      return _buildEmptyState(context, theme);
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOverallPrediction(context, theme, predictions),
          const SizedBox(height: AppConfig.paddingL),
          _buildPredictionChart(context, theme, predictions),
          const SizedBox(height: AppConfig.paddingL),
          _buildTopicPredictions(context, theme, predictions),
          const SizedBox(height: AppConfig.paddingL),
          _buildRecommendations(context, theme, predictions),
        ],
      ),
    );
  }

  Widget _buildOverallPrediction(BuildContext context, ThemeData theme, List<PerformancePrediction> predictions) {
    final avgPrediction = predictions.map((p) => p.predictedScore).reduce((a, b) => a + b) / predictions.length;
    final avgConfidence = predictions.map((p) => p.confidenceLevel).reduce((a, b) => a + b) / predictions.length;
    
    final predictionColor = _getPredictionColor(avgPrediction);
    final confidenceColor = _getConfidenceColor(avgConfidence);

    return Card(
      elevation: AppConfig.elevationM,
      child: Container(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConfig.radiusM),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              predictionColor.withOpacity(0.1),
              predictionColor.withOpacity(0.05),
            ],
          ),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppConfig.paddingM),
                  decoration: BoxDecoration(
                    color: predictionColor.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.trending_up,
                    color: predictionColor,
                    size: 32,
                  ),
                ),
                const SizedBox(width: AppConfig.paddingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'التوقع العام للأداء',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppConfig.paddingXS),
                      Text(
                        'خلال ${_getTimeframeText(_selectedTimeframe)}',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConfig.paddingL),
            Row(
              children: [
                Expanded(
                  child: _buildPredictionMetric(
                    'النتيجة المتوقعة',
                    '${avgPrediction.toInt()}%',
                    predictionColor,
                    Icons.score,
                  ),
                ),
                const SizedBox(width: AppConfig.paddingM),
                Expanded(
                  child: _buildPredictionMetric(
                    'مستوى الثقة',
                    '${(avgConfidence * 100).toInt()}%',
                    confidenceColor,
                    Icons.verified,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConfig.paddingM),
            LinearProgressIndicator(
              value: avgPrediction / 100,
              backgroundColor: predictionColor.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(predictionColor),
              minHeight: 8,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPredictionMetric(String title, String value, Color color, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: AppConfig.paddingXS),
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          title,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildPredictionChart(BuildContext context, ThemeData theme, List<PerformancePrediction> predictions) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'توزيع التنبؤات حسب الموضوع',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.paddingM),
            SizedBox(
              height: 200,
              child: BarChart(
                _buildBarChartData(predictions, theme),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopicPredictions(BuildContext context, ThemeData theme, List<PerformancePrediction> predictions) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تنبؤات تفصيلية حسب الموضوع',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.paddingM),
            ...predictions.map((prediction) => _buildTopicPredictionTile(context, theme, prediction)),
          ],
        ),
      ),
    );
  }

  Widget _buildTopicPredictionTile(BuildContext context, ThemeData theme, PerformancePrediction prediction) {
    final predictionColor = _getPredictionColor(prediction.predictedScore);
    final accuracyIcon = _getAccuracyIcon(prediction.accuracy);
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.paddingM),
      padding: const EdgeInsets.all(AppConfig.paddingM),
      decoration: BoxDecoration(
        color: predictionColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConfig.radiusS),
        border: Border.all(color: predictionColor.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  prediction.topicName,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConfig.paddingS,
                  vertical: AppConfig.paddingXS,
                ),
                decoration: BoxDecoration(
                  color: predictionColor,
                  borderRadius: BorderRadius.circular(AppConfig.radiusS),
                ),
                child: Text(
                  '${prediction.predictedScore.toInt()}%',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConfig.paddingS),
          Row(
            children: [
              Icon(
                accuracyIcon,
                size: 16,
                color: _getAccuracyColor(prediction.accuracy),
              ),
              const SizedBox(width: AppConfig.paddingXS),
              Text(
                'دقة التنبؤ: ${_getAccuracyText(prediction.accuracy)}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: _getAccuracyColor(prediction.accuracy),
                ),
              ),
              const Spacer(),
              Text(
                'الثقة: ${(prediction.confidenceLevel * 100).toInt()}%',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConfig.paddingS),
          LinearProgressIndicator(
            value: prediction.predictedScore / 100,
            backgroundColor: predictionColor.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(predictionColor),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendations(BuildContext context, ThemeData theme, List<PerformancePrediction> predictions) {
    final allRecommendations = predictions
        .expand((p) => p.recommendedActions)
        .toSet()
        .toList();

    if (allRecommendations.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.lightbulb,
                  color: Colors.amber,
                ),
                const SizedBox(width: AppConfig.paddingS),
                Text(
                  'توصيات لتحسين الأداء',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConfig.paddingM),
            ...allRecommendations.take(5).map((recommendation) => Container(
              margin: const EdgeInsets.only(bottom: AppConfig.paddingS),
              padding: const EdgeInsets.all(AppConfig.paddingM),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConfig.radiusS),
                border: Border.all(color: Colors.amber.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.arrow_forward,
                    color: Colors.amber,
                    size: 16,
                  ),
                  const SizedBox(width: AppConfig.paddingS),
                  Expanded(
                    child: Text(
                      recommendation,
                      style: theme.textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            )),
            const SizedBox(height: AppConfig.paddingM),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _createStudyPlan(predictions),
                icon: const Icon(Icons.schedule),
                label: const Text('إنشاء خطة دراسية مخصصة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber,
                  foregroundColor: Colors.black,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.auto_awesome,
            size: 80,
            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.5),
          ),
          const SizedBox(height: AppConfig.paddingM),
          Text(
            'لا توجد بيانات كافية للتنبؤ',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppConfig.paddingS),
          Text(
            'قم بإجراء المزيد من الاختبارات لتوليد تنبؤات دقيقة',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConfig.paddingL),
          ElevatedButton.icon(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.quiz),
            label: const Text('ابدأ اختبار'),
          ),
        ],
      ),
    );
  }

  BarChartData _buildBarChartData(List<PerformancePrediction> predictions, ThemeData theme) {
    final barGroups = predictions.asMap().entries.map((entry) {
      final index = entry.key;
      final prediction = entry.value;
      
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: prediction.predictedScore,
            color: _getPredictionColor(prediction.predictedScore),
            width: 20,
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      );
    }).toList();

    return BarChartData(
      alignment: BarChartAlignment.spaceAround,
      maxY: 100,
      barTouchData: BarTouchData(
        touchTooltipData: BarTouchTooltipData(
          getTooltipItem: (group, groupIndex, rod, rodIndex) {
            final prediction = predictions[groupIndex];
            return BarTooltipItem(
              '${prediction.topicName}\n${prediction.predictedScore.toInt()}%',
              const TextStyle(color: Colors.white),
            );
          },
        ),
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            getTitlesWidget: (value, meta) {
              final index = value.toInt();
              if (index >= 0 && index < predictions.length) {
                return SideTitleWidget(
                  axisSide: meta.axisSide,
                  child: Text(
                    predictions[index].topicName.length > 8
                        ? '${predictions[index].topicName.substring(0, 8)}...'
                        : predictions[index].topicName,
                    style: const TextStyle(fontSize: 10),
                  ),
                );
              }
              return const Text('');
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: 20,
            getTitlesWidget: (value, meta) {
              return Text(
                '${value.toInt()}%',
                style: const TextStyle(fontSize: 10),
              );
            },
          ),
        ),
      ),
      borderData: FlBorderData(show: false),
      barGroups: barGroups,
    );
  }

  Color _getPredictionColor(double score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.orange;
    return Colors.red;
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) return Colors.green;
    if (confidence >= 0.6) return Colors.orange;
    return Colors.red;
  }

  IconData _getAccuracyIcon(PredictionAccuracy accuracy) {
    switch (accuracy) {
      case PredictionAccuracy.veryHigh:
        return Icons.verified;
      case PredictionAccuracy.high:
        return Icons.check_circle;
      case PredictionAccuracy.medium:
        return Icons.info;
      case PredictionAccuracy.low:
        return Icons.warning;
    }
  }

  Color _getAccuracyColor(PredictionAccuracy accuracy) {
    switch (accuracy) {
      case PredictionAccuracy.veryHigh:
        return Colors.green;
      case PredictionAccuracy.high:
        return Colors.lightGreen;
      case PredictionAccuracy.medium:
        return Colors.orange;
      case PredictionAccuracy.low:
        return Colors.red;
    }
  }

  String _getAccuracyText(PredictionAccuracy accuracy) {
    switch (accuracy) {
      case PredictionAccuracy.veryHigh:
        return 'عالية جداً';
      case PredictionAccuracy.high:
        return 'عالية';
      case PredictionAccuracy.medium:
        return 'متوسطة';
      case PredictionAccuracy.low:
        return 'منخفضة';
    }
  }

  String _getTimeframeText(String timeframe) {
    switch (timeframe) {
      case '7':
        return 'الأسبوع القادم';
      case '14':
        return 'الأسبوعين القادمين';
      case '30':
        return 'الشهر القادم';
      default:
        return 'الفترة القادمة';
    }
  }

  void _createStudyPlan(List<PerformancePrediction> predictions) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطة دراسية مخصصة'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('بناءً على تنبؤات الأداء، نقترح عليك:'),
              const SizedBox(height: 16),
              ...predictions.take(3).map((prediction) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Text(
                  '• ${prediction.topicName}: ${prediction.predictedScore < 70 ? "يحتاج تركيز إضافي" : "حافظ على المستوى"}',
                ),
              )).toList(),
              const SizedBox(height: 16),
              const Text(
                'سيتم إنشاء خطة دراسية مفصلة قريباً.',
                style: TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  Future<void> _refreshData() async {
    setState(() {
      _isLoading = true;
    });
    await _analyticsService.refreshAnalytics();
    setState(() {
      _isLoading = false;
    });
  }
}
