import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'dart:math' as math;
import '../config/app_config.dart';
import '../models/settings_model.dart';
import '../services/settings_service.dart';
import '../services/storage_service.dart';
import '../models/quiz_model.dart';
import '../models/quiz_result_model.dart';
import '../utils/app_localizations.dart';
import '../widgets/language_switcher.dart';
import '../app.dart';

// Providers for home screen data
final recentQuizzesProvider = FutureProvider<List<Quiz>>((ref) async {
  try {
    final storageService = StorageService();
    await storageService.initialize();
    final allQuizzes = await storageService.getAllQuizzes();
    return allQuizzes.take(3).toList();
  } catch (e) {
    return <Quiz>[];
  }
});

final recentResultsProvider = FutureProvider<List<QuizResult>>((ref) async {
  try {
    final storageService = StorageService();
    await storageService.initialize();
    final allResults = await storageService.getAllQuizResults();
    return allResults.take(3).toList();
  } catch (e) {
    return <QuizResult>[];
  }
});

final statsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  try {
    final storageService = StorageService();
    await storageService.initialize();

    final allQuizzes = await storageService.getAllQuizzes();
    final allResults = await storageService.getAllQuizResults();

    final totalQuizzes = allQuizzes.length;
    final totalResults = allResults.length;

    // Calculate real average score
    final averageScore = allResults.isEmpty
        ? 0.0
        : allResults.map((r) => r.scorePercentage).reduce((a, b) => a + b) / allResults.length;

    // Calculate completed this week
    final now = DateTime.now();
    final weekAgo = now.subtract(const Duration(days: 7));
    final completedThisWeek = allResults.where((r) {
      final completedAt = r.completedAt ?? r.startedAt;
      return completedAt.isAfter(weekAgo);
    }).length;

    // Calculate completed this month
    final monthAgo = now.subtract(const Duration(days: 30));
    final completedThisMonth = allResults.where((r) {
      final completedAt = r.completedAt ?? r.startedAt;
      return completedAt.isAfter(monthAgo);
    }).length;

    // Calculate best score
    final bestScore = allResults.isEmpty
        ? 0.0
        : allResults.map((r) => r.scorePercentage).reduce((a, b) => a > b ? a : b);

    // Calculate total questions answered
    final totalQuestionsAnswered = allResults.fold<int>(
      0,
      (sum, result) => sum + result.questionResults.length,
    );

    // Calculate correct answers
    final totalCorrectAnswers = allResults.fold<int>(
      0,
      (sum, result) => sum + result.questionResults.where((q) => q.isCorrect).length,
    );

    // Calculate study time (total time spent on quizzes)
    final totalStudyTimeMinutes = allResults.fold<int>(
      0,
      (sum, result) {
        final duration = result.completedAt?.difference(result.startedAt) ?? Duration.zero;
        return sum + duration.inMinutes;
      },
    );

    return {
      'totalQuizzes': totalQuizzes,
      'totalResults': totalResults,
      'averageScore': averageScore,
      'bestScore': bestScore,
      'completedThisWeek': completedThisWeek,
      'completedThisMonth': completedThisMonth,
      'totalQuestionsAnswered': totalQuestionsAnswered,
      'totalCorrectAnswers': totalCorrectAnswers,
      'totalStudyTimeMinutes': totalStudyTimeMinutes,
      'accuracyRate': totalQuestionsAnswered > 0
          ? (totalCorrectAnswers / totalQuestionsAnswered) * 100
          : 0.0,
    };
  } catch (e) {
    return {
      'totalQuizzes': 0,
      'totalResults': 0,
      'averageScore': 0.0,
      'bestScore': 0.0,
      'completedThisWeek': 0,
      'completedThisMonth': 0,
      'totalQuestionsAnswered': 0,
      'totalCorrectAnswers': 0,
      'totalStudyTimeMinutes': 0,
      'accuracyRate': 0.0,
    };
  }
});

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _fabAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<double>(
      begin: 50.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
    ));

    _fabAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
    Future.delayed(const Duration(milliseconds: 600), () {
      _fabAnimationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final settings = ref.watch(appSettingsProvider);
    final recentQuizzesAsync = ref.watch(recentQuizzesProvider);
    final recentResultsAsync = ref.watch(recentResultsProvider);
    final statsAsync = ref.watch(statsProvider);

    return Scaffold(
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return CustomScrollView(
            physics: settings.reduceMotion 
                ? const NeverScrollableScrollPhysics()
                : const BouncingScrollPhysics(),
            slivers: [
              _buildAppBar(context, theme, settings),
              SliverPadding(
                padding: const EdgeInsets.all(AppConfig.paddingM),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    Transform.translate(
                      offset: Offset(0, _slideAnimation.value),
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: _buildWelcomeCard(context, theme, settings),
                      ),
                    ),
                    const SizedBox(height: AppConfig.paddingL),
                    Transform.translate(
                      offset: Offset(0, _slideAnimation.value * 1.5),
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: _buildStatsSection(context, theme, statsAsync),
                      ),
                    ),
                    const SizedBox(height: AppConfig.paddingL),
                    Transform.translate(
                      offset: Offset(0, _slideAnimation.value * 2),
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: _buildQuickActions(context, theme, settings),
                      ),
                    ),
                    const SizedBox(height: AppConfig.paddingL),
                    Transform.translate(
                      offset: Offset(0, _slideAnimation.value * 2.5),
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: _buildRecentQuizzes(context, theme, recentQuizzesAsync),
                      ),
                    ),
                    const SizedBox(height: AppConfig.paddingL),
                    Transform.translate(
                      offset: Offset(0, _slideAnimation.value * 3),
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: _buildRecentResults(context, theme, recentResultsAsync),
                      ),
                    ),
                    const SizedBox(height: AppConfig.paddingXL * 2),
                  ]),
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: ScaleTransition(
        scale: _fabAnimation,
        child: FloatingActionButton.extended(
          onPressed: () => context.push('/upload'),
          icon: const Icon(Icons.add),
          label: Text(AppLocalizations.of(context).uploadPdf),
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
        ),
      ),
      bottomNavigationBar: _buildBottomNavigation(context, theme),
    );
  }

  Widget _buildAppBar(BuildContext context, ThemeData theme, AppSettings settings) {
    return SliverAppBar(
      expandedHeight: 120.0,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: theme.colorScheme.surface,
      foregroundColor: theme.colorScheme.onSurface,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          AppConfig.appName,
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.primary.withOpacity(0.1),
                theme.colorScheme.secondary.withOpacity(0.1),
              ],
            ),
          ),
        ),
      ),
      actions: [
        const LanguageSwitcher(showLabel: false, isCompact: true),
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () {
            // TODO: Implement notifications
          },
        ),
        IconButton(
          icon: const Icon(Icons.settings_outlined),
          onPressed: () => context.push('/settings'),
        ),
      ],
    );
  }

  Widget _buildWelcomeCard(BuildContext context, ThemeData theme, AppSettings settings) {
    final hour = DateTime.now().hour;
    String greeting;
    IconData greetingIcon;
    
    if (hour < 12) {
      greeting = 'صباح الخير';
      greetingIcon = Icons.wb_sunny;
    } else if (hour < 17) {
      greeting = 'مساء الخير';
      greetingIcon = Icons.wb_sunny_outlined;
    } else {
      greeting = 'مساء الخير';
      greetingIcon = Icons.nights_stay;
    }

    return Card(
      elevation: AppConfig.elevationM,
      child: Container(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConfig.radiusM),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primary,
              theme.colorScheme.secondary,
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  greetingIcon,
                  color: Colors.white,
                  size: 28,
                ),
                const SizedBox(width: AppConfig.paddingS),
                Text(
                  greeting,
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConfig.paddingS),
            Text(
              'مرحباً بك في تطبيق QuePDF',
              style: theme.textTheme.titleMedium?.copyWith(
                color: Colors.white.withOpacity(0.9),
              ),
            ),
            const SizedBox(height: AppConfig.paddingXS),
            Text(
              'حول ملفات PDF إلى اختبارات تفاعلية ',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection(BuildContext context, ThemeData theme, AsyncValue<Map<String, dynamic>> statsAsync) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائياتك',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConfig.paddingM),
        statsAsync.when(
          data: (stats) => _buildStatsGrid(context, theme, stats),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorWidget(context, theme, 'خطأ في تحميل الإحصائيات'),
        ),
      ],
    );
  }

  Widget _buildStatsGrid(BuildContext context, ThemeData theme, Map<String, dynamic> stats) {
    final statItems = [
      {
        'title': 'إجمالي الاختبارات',
        'value': '${stats['totalQuizzes']}',
        'icon': Icons.quiz,
        'color': AppConfig.primaryColor,
      },
      {
        'title': 'أفضل نتيجة',
        'value': '${stats['bestScore'].toStringAsFixed(1)}%',
        'icon': Icons.star,
        'color': Colors.amber,
      },
      {
        'title': 'معدل الدقة',
        'value': '${stats['accuracyRate'].toStringAsFixed(1)}%',
        'icon': Icons.gps_fixed,
        'color': AppConfig.successColor,
      },
      {
        'title': 'وقت الدراسة',
        'value': _formatStudyTime(stats['totalStudyTimeMinutes']),
        'icon': Icons.access_time,
        'color': AppConfig.accentColor,
      },
      {
        'title': 'الأسئلة المجابة',
        'value': '${stats['totalQuestionsAnswered']}',
        'icon': Icons.help_outline,
        'color': AppConfig.secondaryColor,
      },
      {
        'title': 'هذا الشهر',
        'value': '${stats['completedThisMonth']}',
        'icon': Icons.calendar_month,
        'color': Colors.purple,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppConfig.paddingM,
        mainAxisSpacing: AppConfig.paddingM,
        childAspectRatio: 1.3,
      ),
      itemCount: statItems.length,
      itemBuilder: (context, index) {
        final item = statItems[index];
        return _buildStatCard(
          context,
          theme,
          item['title'] as String,
          item['value'] as String,
          item['icon'] as IconData,
          item['color'] as Color,
        );
      },
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    ThemeData theme,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: AppConfig.elevationS,
      child: Container(
        padding: const EdgeInsets.all(AppConfig.paddingM),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConfig.radiusM),
          border: Border.all(
            color: color.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 32,
            ),
            const SizedBox(height: AppConfig.paddingS),
            Text(
              value,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: AppConfig.paddingXS),
            Text(
              title,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, ThemeData theme, AppSettings settings) {
    final localizations = AppLocalizations.of(context);

    final actions = [
      {
        'title': localizations.uploadPdf,
        'subtitle': 'ابدأ اختبار جديد',
        'icon': Icons.upload_file,
        'color': AppConfig.primaryColor,
        'route': '/upload',
      },
      {
        'title': 'تاريخ الاختبارات',
        'subtitle': 'عرض الاختبارات السابقة',
        'icon': Icons.history,
        'color': AppConfig.accentColor,
        'route': '/history',
      },
      {
        'title': localizations.advancedAnalytics,
        'subtitle': 'رؤى ذكية لأدائك',
        'icon': Icons.analytics,
        'color': Colors.purple,
        'route': '/analytics',
      },
      {
        'title': localizations.settings,
        'subtitle': 'تخصيص التطبيق',
        'icon': Icons.settings,
        'color': AppConfig.secondaryColor,
        'route': '/settings',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإجراءات السريعة',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConfig.paddingM),
        ...actions.map((action) => _buildActionCard(
          context,
          theme,
          action['title'] as String,
          action['subtitle'] as String,
          action['icon'] as IconData,
          action['color'] as Color,
          action['route'] as String,
          settings.enableAnimations,
        )).toList(),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    ThemeData theme,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    String route,
    bool enableAnimations,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.paddingM),
      child: Card(
        elevation: AppConfig.elevationS,
        child: InkWell(
          onTap: () => context.push(route),
          borderRadius: BorderRadius.circular(AppConfig.radiusM),
          child: Container(
            padding: const EdgeInsets.all(AppConfig.paddingM),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppConfig.paddingM),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppConfig.radiusS),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppConfig.paddingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: AppConfig.paddingXS),
                      Text(
                        subtitle,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: theme.colorScheme.onSurfaceVariant,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRecentQuizzes(BuildContext context, ThemeData theme, AsyncValue<List<Quiz>> quizzesAsync) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'الاختبارات الحديثة',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => context.push('/history'),
              child: Text(AppLocalizations.of(context).viewAll),
            ),
          ],
        ),
        const SizedBox(height: AppConfig.paddingM),
        quizzesAsync.when(
          data: (quizzes) => quizzes.isEmpty
              ? _buildEmptyState(context, theme, 'لا توجد اختبارات حديثة', Icons.quiz)
              : Column(
                  children: quizzes.map((quiz) => _buildQuizCard(context, theme, quiz)).toList(),
                ),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorWidget(context, theme, 'خطأ في تحميل الاختبارات'),
        ),
      ],
    );
  }

  Widget _buildRecentResults(BuildContext context, ThemeData theme, AsyncValue<List<QuizResult>> resultsAsync) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'النتائج الحديثة',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => context.push('/history'),
              child: Text(AppLocalizations.of(context).viewAll),
            ),
          ],
        ),
        const SizedBox(height: AppConfig.paddingM),
        resultsAsync.when(
          data: (results) => results.isEmpty
              ? _buildEmptyState(context, theme, 'لا توجد نتائج حديثة', Icons.assessment)
              : Column(
                  children: results.map((result) => _buildResultCard(context, theme, result)).toList(),
                ),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorWidget(context, theme, 'خطأ في تحميل النتائج'),
        ),
      ],
    );
  }

  Widget _buildQuizCard(BuildContext context, ThemeData theme, Quiz quiz) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConfig.paddingS),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: theme.colorScheme.primary,
          child: Text(
            quiz.questions.length.toString(),
            style: TextStyle(
              color: theme.colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          quiz.title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(quiz.description),
        trailing: const Icon(Icons.play_arrow),
        onTap: () => context.push('/quiz/${quiz.id}'),
      ),
    );
  }

  Widget _buildResultCard(BuildContext context, ThemeData theme, QuizResult result) {
    final scoreColor = result.scorePercentage >= 70
        ? AppConfig.successColor
        : result.scorePercentage >= 50
            ? AppConfig.warningColor
            : AppConfig.errorColor;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConfig.paddingS),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: scoreColor,
          child: Text(
            '${result.scorePercentage.toInt()}%',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
        title: Text(
          result.quizTitle,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          '${result.questionResults.where((q) => q.isCorrect).length}/${result.questionResults.length} صحيح',
        ),
        trailing: const Icon(Icons.visibility),
        onTap: () => context.push('/results/${result.id}'),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, ThemeData theme, String message, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.paddingL),
      child: Column(
        children: [
          Icon(
            icon,
            size: 48,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: AppConfig.paddingM),
          Text(
            message,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context, ThemeData theme, String message) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: theme.colorScheme.error,
          ),
          const SizedBox(width: AppConfig.paddingS),
          Expanded(
            child: Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation(BuildContext context, ThemeData theme) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: 0,
      onTap: (index) {
        switch (index) {
          case 0:
            // Already on home
            break;
          case 1:
            context.push('/upload');
            break;
          case 2:
            context.push('/history');
            break;
          case 3:
            context.push('/settings');
            break;
        }
      },
      items: [
        BottomNavigationBarItem(
          icon: const Icon(Icons.home),
          label: 'الرئيسية',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.upload),
          label: 'رفع',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.history),
          label: AppLocalizations.of(context).history,
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.settings),
          label: AppLocalizations.of(context).settings,
        ),
      ],
    );
  }

  String _formatStudyTime(int minutes) {
    if (minutes < 60) {
      return '${minutes}د';
    } else if (minutes < 1440) { // Less than 24 hours
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '${hours}س';
      } else {
        return '${hours}س ${remainingMinutes}د';
      }
    } else {
      final days = minutes ~/ 1440;
      final remainingHours = (minutes % 1440) ~/ 60;
      if (remainingHours == 0) {
        return '${days}ي';
      } else {
        return '${days}ي ${remainingHours}س';
      }
    }
  }
}
