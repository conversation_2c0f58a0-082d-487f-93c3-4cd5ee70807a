import 'package:flutter/material.dart';
import '../config/app_config.dart';
import '../models/analytics_models.dart';
import '../services/advanced_analytics_service.dart';

class ErrorAnalysisScreen extends StatefulWidget {
  const ErrorAnalysisScreen({super.key});

  @override
  State<ErrorAnalysisScreen> createState() => _ErrorAnalysisScreenState();
}

class _ErrorAnalysisScreenState extends State<ErrorAnalysisScreen>
    with TickerProviderStateMixin {
  final AdvancedAnalyticsService _analyticsService = AdvancedAnalyticsService();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  bool _isLoading = true;
  String _selectedTopic = 'all';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );
    _loadData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    await _analyticsService.initialize();
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('تحليل الأخطاء الشائعة'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(context),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : FadeTransition(
              opacity: _fadeAnimation,
              child: _buildContent(context, theme),
            ),
    );
  }

  Widget _buildContent(BuildContext context, ThemeData theme) {
    final errorPatterns = _analyticsService.errorPatterns;
    
    if (errorPatterns == null || errorPatterns.isEmpty) {
      return _buildEmptyState(context, theme);
    }

    final filteredPatterns = _selectedTopic == 'all'
        ? errorPatterns
        : errorPatterns.where((p) => p.topicName == _selectedTopic).toList();

    return Column(
      children: [
        _buildTopicFilter(context, theme, errorPatterns),
        _buildSummaryCards(context, theme, filteredPatterns),
        Expanded(
          child: _buildErrorPatternsList(context, theme, filteredPatterns),
        ),
      ],
    );
  }

  Widget _buildTopicFilter(BuildContext context, ThemeData theme, List<ErrorPattern> patterns) {
    final topics = ['all', ...patterns.map((p) => p.topicName).toSet().toList()];
    
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: AppConfig.paddingM),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: topics.length,
        itemBuilder: (context, index) {
          final topic = topics[index];
          final isSelected = _selectedTopic == topic;
          
          return Padding(
            padding: const EdgeInsets.only(right: AppConfig.paddingS),
            child: FilterChip(
              label: Text(topic == 'all' ? 'جميع المواضيع' : topic),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedTopic = topic;
                });
              },
              backgroundColor: theme.colorScheme.surfaceVariant,
              selectedColor: theme.colorScheme.primary.withOpacity(0.2),
              checkmarkColor: theme.colorScheme.primary,
            ),
          );
        },
      ),
    );
  }

  Widget _buildSummaryCards(BuildContext context, ThemeData theme, List<ErrorPattern> patterns) {
    final totalErrors = patterns.fold<int>(0, (sum, pattern) => sum + pattern.frequency);
    final mostCommonError = patterns.isNotEmpty
        ? patterns.reduce((a, b) => a.frequency > b.frequency ? a : b)
        : null;
    final avgImpact = patterns.isNotEmpty
        ? patterns.map((p) => p.impactOnPerformance).reduce((a, b) => a + b) / patterns.length
        : 0.0;

    return Padding(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'إجمالي الأخطاء',
              totalErrors.toString(),
              Icons.error_outline,
              Colors.red,
              theme,
            ),
          ),
          const SizedBox(width: AppConfig.paddingS),
          Expanded(
            child: _buildSummaryCard(
              'أكثر الأخطاء',
              _getErrorTypeTitle(mostCommonError?.errorType ?? ErrorType.conceptual),
              Icons.trending_up,
              Colors.orange,
              theme,
            ),
          ),
          const SizedBox(width: AppConfig.paddingS),
          Expanded(
            child: _buildSummaryCard(
              'متوسط التأثير',
              '${(avgImpact * 100).toInt()}%',
              Icons.trending_down,
              Colors.blue,
              theme,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingM),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: AppConfig.paddingXS),
            Text(
              value,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: theme.textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorPatternsList(BuildContext context, ThemeData theme, List<ErrorPattern> patterns) {
    if (patterns.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle,
              size: 80,
              color: Colors.green.withOpacity(0.5),
            ),
            const SizedBox(height: AppConfig.paddingM),
            Text(
              'لا توجد أخطاء شائعة في هذا الموضوع',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: AppConfig.paddingS),
            Text(
              'أداؤك ممتاز في هذا المجال!',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.green,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      itemCount: patterns.length,
      itemBuilder: (context, index) {
        final pattern = patterns[index];
        return _buildErrorPatternCard(context, theme, pattern);
      },
    );
  }

  Widget _buildErrorPatternCard(BuildContext context, ThemeData theme, ErrorPattern pattern) {
    final errorColor = _getErrorTypeColor(pattern.errorType);
    
    return Card(
      margin: const EdgeInsets.only(bottom: AppConfig.paddingM),
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(AppConfig.paddingS),
          decoration: BoxDecoration(
            color: errorColor.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(
            _getErrorTypeIcon(pattern.errorType),
            color: errorColor,
            size: 20,
          ),
        ),
        title: Text(
          _getErrorTypeTitle(pattern.errorType),
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الموضوع: ${pattern.topicName}'),
            const SizedBox(height: 4),
            Row(
              children: [
                Text('التكرار: ${pattern.frequency}'),
                const SizedBox(width: AppConfig.paddingM),
                Text('التأثير: ${(pattern.impactOnPerformance * 100).toInt()}%'),
              ],
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(AppConfig.paddingL),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Common Mistakes
                if (pattern.commonMistakes.isNotEmpty) ...[
                  Text(
                    'الأخطاء الشائعة:',
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConfig.paddingS),
                  ...pattern.commonMistakes.map((mistake) => Padding(
                    padding: const EdgeInsets.only(bottom: AppConfig.paddingXS),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('• '),
                        Expanded(child: Text(mistake)),
                      ],
                    ),
                  )),
                  const SizedBox(height: AppConfig.paddingM),
                ],
                
                // Suggested Solutions
                Text(
                  'الحلول المقترحة:',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(height: AppConfig.paddingS),
                ...pattern.suggestedSolutions.map((solution) => Container(
                  margin: const EdgeInsets.only(bottom: AppConfig.paddingS),
                  padding: const EdgeInsets.all(AppConfig.paddingS),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppConfig.radiusS),
                    border: Border.all(color: Colors.green.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.lightbulb,
                        color: Colors.green,
                        size: 16,
                      ),
                      const SizedBox(width: AppConfig.paddingS),
                      Expanded(child: Text(solution)),
                    ],
                  ),
                )),
                
                // Action Buttons
                const SizedBox(height: AppConfig.paddingM),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _practiceMore(pattern.topicName, pattern.errorType),
                        icon: const Icon(Icons.play_arrow),
                        label: const Text('تدرب أكثر'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: errorColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: AppConfig.paddingS),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _createTargetedQuiz(pattern),
                        icon: const Icon(Icons.quiz),
                        label: const Text('اختبار مستهدف'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.psychology,
            size: 80,
            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.5),
          ),
          const SizedBox(height: AppConfig.paddingM),
          Text(
            'لا توجد أنماط أخطاء كافية للتحليل',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppConfig.paddingS),
          Text(
            'قم بإجراء المزيد من الاختبارات لتحليل أنماط الأخطاء',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConfig.paddingL),
          ElevatedButton.icon(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.quiz),
            label: const Text('ابدأ اختبار'),
          ),
        ],
      ),
    );
  }

  String _getErrorTypeTitle(ErrorType errorType) {
    switch (errorType) {
      case ErrorType.conceptual:
        return 'أخطاء مفاهيمية';
      case ErrorType.procedural:
        return 'أخطاء إجرائية';
      case ErrorType.computational:
        return 'أخطاء حسابية';
      case ErrorType.reading:
        return 'أخطاء قراءة';
      case ErrorType.timeManagement:
        return 'إدارة الوقت';
      case ErrorType.careless:
        return 'أخطاء عدم التركيز';
    }
  }

  IconData _getErrorTypeIcon(ErrorType errorType) {
    switch (errorType) {
      case ErrorType.conceptual:
        return Icons.psychology;
      case ErrorType.procedural:
        return Icons.list_alt;
      case ErrorType.computational:
        return Icons.calculate;
      case ErrorType.reading:
        return Icons.visibility;
      case ErrorType.timeManagement:
        return Icons.timer;
      case ErrorType.careless:
        return Icons.warning;
    }
  }

  Color _getErrorTypeColor(ErrorType errorType) {
    switch (errorType) {
      case ErrorType.conceptual:
        return Colors.red;
      case ErrorType.procedural:
        return Colors.orange;
      case ErrorType.computational:
        return Colors.blue;
      case ErrorType.reading:
        return Colors.purple;
      case ErrorType.timeManagement:
        return Colors.amber;
      case ErrorType.careless:
        return Colors.pink;
    }
  }

  void _practiceMore(String topicName, ErrorType errorType) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سيتم إنشاء تدريبات مخصصة لـ: $topicName'),
        action: SnackBarAction(
          label: 'ابدأ',
          onPressed: () {
            // Navigate to targeted practice
          },
        ),
      ),
    );
  }

  void _createTargetedQuiz(ErrorPattern pattern) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سيتم إنشاء اختبار مستهدف لمعالجة: ${_getErrorTypeTitle(pattern.errorType)}'),
        action: SnackBarAction(
          label: 'إنشاء',
          onPressed: () {
            // Navigate to quiz creation with specific focus
          },
        ),
      ),
    );
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فهم تحليل الأخطاء'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('🔴 أخطاء مفاهيمية: سوء فهم المفهوم الأساسي'),
              SizedBox(height: 8),
              Text('🟠 أخطاء إجرائية: خطأ في تطبيق الخطوات'),
              SizedBox(height: 8),
              Text('🔵 أخطاء حسابية: خطأ في العمليات الحسابية'),
              SizedBox(height: 8),
              Text('🟣 أخطاء قراءة: عدم فهم السؤال بشكل صحيح'),
              SizedBox(height: 8),
              Text('🟡 إدارة الوقت: استغراق وقت طويل أو قصير جداً'),
              SizedBox(height: 8),
              Text('🩷 عدم التركيز: أخطاء بسبب عدم الانتباه'),
              SizedBox(height: 16),
              Text(
                'استخدم هذا التحليل لتحديد نقاط ضعفك وتحسين أدائك.',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }

  Future<void> _refreshData() async {
    setState(() {
      _isLoading = true;
    });
    await _analyticsService.refreshAnalytics();
    setState(() {
      _isLoading = false;
    });
  }
}
