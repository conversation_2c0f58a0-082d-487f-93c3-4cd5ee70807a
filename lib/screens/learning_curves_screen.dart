import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'dart:math' as math;
import '../config/app_config.dart';
import '../models/analytics_models.dart';
import '../services/advanced_analytics_service.dart';
import '../services/storage_service.dart';
import '../utils/app_localizations.dart';

class LearningCurvesScreen extends StatefulWidget {
  const LearningCurvesScreen({super.key});

  @override
  State<LearningCurvesScreen> createState() => _LearningCurvesScreenState();
}

class _LearningCurvesScreenState extends State<LearningCurvesScreen>
    with TickerProviderStateMixin {
  final AdvancedAnalyticsService _analyticsService = AdvancedAnalyticsService();
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  
  bool _isLoading = true;
  String _selectedMetric = 'score'; // score, accuracy, speed
  String _selectedPeriod = '30'; // 7, 30, 90 days

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _slideAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );
    _loadData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    await _analyticsService.initialize();
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).learningCurves),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _refreshData,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            onSelected: (value) {
              setState(() {
                _selectedPeriod = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: '7', child: Text('آخر 7 أيام')),
              const PopupMenuItem(value: '30', child: Text('آخر 30 يوم')),
              const PopupMenuItem(value: '90', child: Text('آخر 90 يوم')),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, 1),
                end: Offset.zero,
              ).animate(_slideAnimation),
              child: _buildContent(context, theme),
            ),
    );
  }

  Widget _buildContent(BuildContext context, ThemeData theme) {
    return FutureBuilder<LearningCurve?>(
      future: _loadRealLearningData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        final learningCurve = snapshot.data ?? _createDemoLearningCurve();

        if (learningCurve.dataPoints.isEmpty) {
          return _buildEmptyState(context, theme);
        }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricSelector(context, theme),
          const SizedBox(height: AppConfig.paddingL),
          _buildTrendCard(context, theme, learningCurve),
          const SizedBox(height: AppConfig.paddingL),
          _buildChart(context, theme, learningCurve),
          const SizedBox(height: AppConfig.paddingL),
          _buildImportantEvents(context, theme, learningCurve),
          const SizedBox(height: AppConfig.paddingL),
          _buildInsights(context, theme, learningCurve),
        ],
      ),
    );
      },
    );
  }

  Widget _buildMetricSelector(BuildContext context, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختر المقياس',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.paddingS),
            Row(
              children: [
                Expanded(
                  child: _buildMetricButton(
                    'النقاط',
                    'score',
                    Icons.trending_up,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: AppConfig.paddingS),
                Expanded(
                  child: _buildMetricButton(
                    'الدقة',
                    'accuracy',
                    Icons.gps_fixed,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: AppConfig.paddingS),
                Expanded(
                  child: _buildMetricButton(
                    'السرعة',
                    'speed',
                    Icons.speed,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricButton(String label, String metric, IconData icon, Color color) {
    final isSelected = _selectedMetric == metric;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedMetric = metric;
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(vertical: AppConfig.paddingM),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.2) : Colors.transparent,
          border: Border.all(
            color: isSelected ? color : Colors.grey.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(AppConfig.radiusS),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.grey,
              size: 24,
            ),
            const SizedBox(height: AppConfig.paddingXS),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? color : Colors.grey,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendCard(BuildContext context, ThemeData theme, LearningCurve learningCurve) {
    final trendInfo = _getTrendInfo(learningCurve.trend);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(AppConfig.paddingM),
              decoration: BoxDecoration(
                color: trendInfo['color'].withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                trendInfo['icon'],
                color: trendInfo['color'],
                size: 32,
              ),
            ),
            const SizedBox(width: AppConfig.paddingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'اتجاه التعلم',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConfig.paddingXS),
                  Text(
                    trendInfo['title'],
                    style: theme.textTheme.headlineSmall?.copyWith(
                      color: trendInfo['color'],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    trendInfo['description'],
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChart(BuildContext context, ThemeData theme, LearningCurve learningCurve) {
    final filteredPoints = _getFilteredDataPoints(learningCurve.dataPoints);
    
    if (filteredPoints.isEmpty) {
      return Card(
        child: Container(
          height: 300,
          padding: const EdgeInsets.all(AppConfig.paddingL),
          child: const Center(
            child: Text('لا توجد بيانات للفترة المحددة'),
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'منحنى ${_getMetricTitle(_selectedMetric)}',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.paddingM),
            SizedBox(
              height: 300,
              child: LineChart(
                _buildLineChartData(filteredPoints, theme),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImportantEvents(BuildContext context, ThemeData theme, LearningCurve learningCurve) {
    final importantEvents = learningCurve.dataPoints
        .where((point) => point.isImportantEvent)
        .toList();
    
    if (importantEvents.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الأحداث المهمة',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.paddingM),
            ...importantEvents.map((event) => _buildEventTile(context, theme, event)),
          ],
        ),
      ),
    );
  }

  Widget _buildEventTile(BuildContext context, ThemeData theme, LearningPoint event) {
    final isPositive = event.score >= 0.9;
    final color = isPositive ? Colors.green : Colors.red;
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.paddingS),
      padding: const EdgeInsets.all(AppConfig.paddingM),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConfig.radiusS),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            isPositive ? Icons.celebration : Icons.warning,
            color: color,
          ),
          const SizedBox(width: AppConfig.paddingS),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  event.eventDescription ?? (isPositive ? 'إنجاز ممتاز!' : 'يحتاج مراجعة'),
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  '${event.topicName} - ${_formatDate(event.timestamp)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${(event.score * 100).toInt()}%',
            style: theme.textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInsights(BuildContext context, ThemeData theme, LearningCurve learningCurve) {
    final insights = _generateInsights(learningCurve);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'رؤى وتوصيات',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.paddingM),
            ...insights.map((insight) => Padding(
              padding: const EdgeInsets.only(bottom: AppConfig.paddingS),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.lightbulb,
                    color: Colors.amber,
                    size: 20,
                  ),
                  const SizedBox(width: AppConfig.paddingS),
                  Expanded(
                    child: Text(
                      insight,
                      style: theme.textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(AppConfig.paddingL),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConfig.paddingXL),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppConfig.paddingL),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.show_chart,
                      size: 64,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: AppConfig.paddingL),
                  Text(
                    'لا توجد بيانات كافية',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConfig.paddingM),
                  Text(
                    'قم بإجراء المزيد من الاختبارات لرؤية منحنيات التعلم الخاصة بك',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppConfig.paddingL),
                  ElevatedButton.icon(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.upload_file),
                    label: const Text('إنشاء اختبار جديد'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  LineChartData _buildLineChartData(List<LearningPoint> points, ThemeData theme) {
    final spots = points.asMap().entries.map((entry) {
      final index = entry.key.toDouble();
      final point = entry.value;
      
      double value;
      switch (_selectedMetric) {
        case 'accuracy':
          value = point.accuracy * 100;
          break;
        case 'speed':
          value = point.speed;
          break;
        default:
          value = point.score * 100;
      }
      
      return FlSpot(index, value);
    }).toList();

    return LineChartData(
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        horizontalInterval: 20,
        verticalInterval: 1,
        getDrawingHorizontalLine: (value) {
          return FlLine(
            color: theme.colorScheme.outline.withOpacity(0.2),
            strokeWidth: 1,
          );
        },
        getDrawingVerticalLine: (value) {
          return FlLine(
            color: theme.colorScheme.outline.withOpacity(0.2),
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: 1,
            getTitlesWidget: (value, meta) {
              final index = value.toInt();
              if (index >= 0 && index < points.length) {
                return SideTitleWidget(
                  axisSide: meta.axisSide,
                  child: Text(
                    _formatDateShort(points[index].timestamp),
                    style: const TextStyle(fontSize: 10),
                  ),
                );
              }
              return const Text('');
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: 20,
            getTitlesWidget: (value, meta) {
              return Text(
                value.toInt().toString(),
                style: const TextStyle(fontSize: 10),
              );
            },
            reservedSize: 42,
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
      ),
      minX: 0,
      maxX: spots.length.toDouble() - 1,
      minY: 0,
      maxY: 100,
      lineBarsData: [
        LineChartBarData(
          spots: spots,
          isCurved: true,
          gradient: LinearGradient(
            colors: [
              _getMetricColor(_selectedMetric),
              _getMetricColor(_selectedMetric).withOpacity(0.3),
            ],
          ),
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: FlDotData(
            show: true,
            getDotPainter: (spot, percent, barData, index) {
              final point = points[index];
              return FlDotCirclePainter(
                radius: point.isImportantEvent ? 6 : 4,
                color: point.isImportantEvent ? Colors.red : _getMetricColor(_selectedMetric),
                strokeWidth: point.isImportantEvent ? 2 : 0,
                strokeColor: Colors.white,
              );
            },
          ),
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              colors: [
                _getMetricColor(_selectedMetric).withOpacity(0.3),
                _getMetricColor(_selectedMetric).withOpacity(0.0),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ],
    );
  }

  List<LearningPoint> _getFilteredDataPoints(List<LearningPoint> points) {
    final days = int.parse(_selectedPeriod);
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    
    return points.where((point) => point.timestamp.isAfter(cutoffDate)).toList();
  }

  Map<String, dynamic> _getTrendInfo(LearningTrend trend) {
    switch (trend) {
      case LearningTrend.improving:
        return {
          'title': 'تحسن مستمر',
          'description': 'أداؤك يتحسن بشكل مستمر، استمر!',
          'icon': Icons.trending_up,
          'color': Colors.green,
        };
      case LearningTrend.declining:
        return {
          'title': 'تراجع في الأداء',
          'description': 'يحتاج لمزيد من التركيز والممارسة',
          'icon': Icons.trending_down,
          'color': Colors.red,
        };
      case LearningTrend.fluctuating:
        return {
          'title': 'أداء متذبذب',
          'description': 'حاول الحفاظ على نمط ثابت في الدراسة',
          'icon': Icons.show_chart,
          'color': Colors.orange,
        };
      default:
        return {
          'title': 'أداء مستقر',
          'description': 'أداؤك ثابت، يمكن تحسينه أكثر',
          'icon': Icons.horizontal_rule,
          'color': Colors.blue,
        };
    }
  }

  String _getMetricTitle(String metric) {
    switch (metric) {
      case 'accuracy':
        return 'الدقة';
      case 'speed':
        return 'السرعة';
      default:
        return 'النقاط';
    }
  }

  Color _getMetricColor(String metric) {
    switch (metric) {
      case 'accuracy':
        return Colors.green;
      case 'speed':
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateShort(DateTime date) {
    return '${date.day}/${date.month}';
  }

  List<String> _generateInsights(LearningCurve learningCurve) {
    final insights = <String>[];
    final points = learningCurve.dataPoints;
    
    if (points.isEmpty) return insights;
    
    // Calculate average performance
    final avgScore = points.map((p) => p.score).reduce((a, b) => a + b) / points.length;
    
    if (avgScore > 0.8) {
      insights.add('أداؤك ممتاز! متوسط نقاطك ${(avgScore * 100).toInt()}%');
    } else if (avgScore > 0.6) {
      insights.add('أداؤك جيد، يمكن تحسينه أكثر. متوسط نقاطك ${(avgScore * 100).toInt()}%');
    } else {
      insights.add('يحتاج أداؤك لمزيد من التحسين. متوسط نقاطك ${(avgScore * 100).toInt()}%');
    }
    
    // Check consistency
    final recentPoints = points.length > 5 ? points.sublist(points.length - 5) : points;
    if (recentPoints.length >= 3) {
      final variance = _calculateVariance(recentPoints.map((p) => p.score).toList());
      if (variance < 0.05) {
        insights.add('أداؤك مستقر ومتسق في الاختبارات الأخيرة');
      } else {
        insights.add('حاول الحفاظ على نمط ثابت في الدراسة لتحسين الاستقرار');
      }
    }
    
    // Speed insights
    final avgSpeed = points.map((p) => p.speed).reduce((a, b) => a + b) / points.length;
    if (avgSpeed > 2.0) {
      insights.add('سرعتك في الإجابة جيدة (${avgSpeed.toStringAsFixed(1)} سؤال/دقيقة)');
    } else {
      insights.add('يمكن تحسين سرعة الإجابة مع الممارسة');
    }
    
    return insights;
  }

  double _calculateVariance(List<double> values) {
    final mean = values.reduce((a, b) => a + b) / values.length;
    final variance = values.map((v) => (v - mean) * (v - mean)).reduce((a, b) => a + b) / values.length;
    return variance;
  }

  Future<void> _refreshData() async {
    setState(() {
      _isLoading = true;
    });
    await _analyticsService.refreshAnalytics();
    setState(() {
      _isLoading = false;
    });
  }

  // Load real learning data from quiz results
  Future<LearningCurve?> _loadRealLearningData() async {
    try {
      // Import storage service to get real quiz results
      final storageService = StorageService();
      final quizResults = await storageService.getAllQuizResults();

      if (quizResults.isEmpty) {
        return null;
      }

      // Convert quiz results to learning points
      final dataPoints = <LearningPoint>[];

      for (final result in quizResults) {
        // Skip if not completed
        if (result.completedAt == null) continue;

        final accuracy = result.correctAnswers / result.totalQuestions;
        final avgTimePerQuestion = result.totalTimeSeconds / result.totalQuestions;
        final speed = 60.0 / avgTimePerQuestion; // Questions per minute

        dataPoints.add(LearningPoint(
          timestamp: result.completedAt!,
          topicName: result.quizTitle,
          quizId: result.quizId,
          score: result.scorePercentage / 100.0, // Convert percentage to decimal
          accuracy: accuracy,
          speed: speed,
          isImportantEvent: result.scorePercentage >= 90 || result.scorePercentage <= 50,
          eventDescription: result.scorePercentage >= 90
              ? 'نتيجة ممتازة!'
              : result.scorePercentage <= 50
                  ? 'يحتاج مراجعة'
                  : null,
        ));
      }

      // Sort by timestamp
      dataPoints.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      // Determine trend
      LearningTrend trend = LearningTrend.stable;
      if (dataPoints.length >= 3) {
        final recentScores = dataPoints.takeLast(3).map((p) => p.score).toList();
        final oldScores = dataPoints.take(3).map((p) => p.score).toList();

        final recentAvg = recentScores.reduce((a, b) => a + b) / recentScores.length;
        final oldAvg = oldScores.reduce((a, b) => a + b) / oldScores.length;

        if (recentAvg > oldAvg + 0.1) {
          trend = LearningTrend.improving;
        } else if (recentAvg < oldAvg - 0.1) {
          trend = LearningTrend.declining;
        }
      }

      return LearningCurve(
        id: 'real_learning_curve',
        userId: 'current_user',
        startDate: dataPoints.first.timestamp,
        endDate: dataPoints.last.timestamp,
        dataPoints: dataPoints,
        trend: trend,
      );
    } catch (e) {
      print('Error loading real learning data: $e');
      return null;
    }
  }

  // Create demo learning curve for testing
  LearningCurve _createDemoLearningCurve() {
    final now = DateTime.now();
    final dataPoints = <LearningPoint>[];

    // Generate 30 days of demo data
    for (int i = 0; i < 30; i++) {
      final date = now.subtract(Duration(days: 29 - i));
      final baseScore = 0.6 + (i * 0.01) + (math.Random().nextDouble() * 0.2 - 0.1);
      final score = math.max(0.0, math.min(1.0, baseScore));

      dataPoints.add(LearningPoint(
        timestamp: date,
        topicName: 'رياضيات',
        quizId: 'demo_quiz_$i',
        score: score,
        accuracy: score + (math.Random().nextDouble() * 0.1 - 0.05),
        speed: 1.5 + (math.Random().nextDouble() * 1.0),
        isImportantEvent: i % 10 == 0, // Every 10th day is important
        eventDescription: i % 10 == 0 ? (score > 0.8 ? 'إنجاز ممتاز!' : 'يحتاج مراجعة') : null,
      ));
    }

    return LearningCurve(
      id: 'demo_learning_curve',
      userId: 'demo_user',
      startDate: now.subtract(const Duration(days: 29)),
      endDate: now,
      dataPoints: dataPoints,
      trend: LearningTrend.improving,
    );
  }
}
