import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../config/app_config.dart';
import '../models/analytics_models.dart';
import '../services/advanced_analytics_service.dart';

class LearningCurvesScreen extends StatefulWidget {
  const LearningCurvesScreen({super.key});

  @override
  State<LearningCurvesScreen> createState() => _LearningCurvesScreenState();
}

class _LearningCurvesScreenState extends State<LearningCurvesScreen>
    with TickerProviderStateMixin {
  final AdvancedAnalyticsService _analyticsService = AdvancedAnalyticsService();
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  
  bool _isLoading = true;
  String _selectedMetric = 'score'; // score, accuracy, speed
  String _selectedPeriod = '30'; // 7, 30, 90 days

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _slideAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );
    _loadData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    await _analyticsService.initialize();
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('منحنيات التعلم'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            onSelected: (value) {
              setState(() {
                _selectedPeriod = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: '7', child: Text('آخر 7 أيام')),
              const PopupMenuItem(value: '30', child: Text('آخر 30 يوم')),
              const PopupMenuItem(value: '90', child: Text('آخر 90 يوم')),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, 1),
                end: Offset.zero,
              ).animate(_slideAnimation),
              child: _buildContent(context, theme),
            ),
    );
  }

  Widget _buildContent(BuildContext context, ThemeData theme) {
    final learningCurve = _analyticsService.learningCurve;
    
    if (learningCurve == null || learningCurve.dataPoints.isEmpty) {
      return _buildEmptyState(context, theme);
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricSelector(context, theme),
          const SizedBox(height: AppConfig.paddingL),
          _buildTrendCard(context, theme, learningCurve),
          const SizedBox(height: AppConfig.paddingL),
          _buildChart(context, theme, learningCurve),
          const SizedBox(height: AppConfig.paddingL),
          _buildImportantEvents(context, theme, learningCurve),
          const SizedBox(height: AppConfig.paddingL),
          _buildInsights(context, theme, learningCurve),
        ],
      ),
    );
  }

  Widget _buildMetricSelector(BuildContext context, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختر المقياس',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.paddingS),
            Row(
              children: [
                Expanded(
                  child: _buildMetricButton(
                    'النقاط',
                    'score',
                    Icons.trending_up,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: AppConfig.paddingS),
                Expanded(
                  child: _buildMetricButton(
                    'الدقة',
                    'accuracy',
                    Icons.gps_fixed,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: AppConfig.paddingS),
                Expanded(
                  child: _buildMetricButton(
                    'السرعة',
                    'speed',
                    Icons.speed,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricButton(String label, String metric, IconData icon, Color color) {
    final isSelected = _selectedMetric == metric;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedMetric = metric;
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(vertical: AppConfig.paddingM),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.2) : Colors.transparent,
          border: Border.all(
            color: isSelected ? color : Colors.grey.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(AppConfig.radiusS),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.grey,
              size: 24,
            ),
            const SizedBox(height: AppConfig.paddingXS),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? color : Colors.grey,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendCard(BuildContext context, ThemeData theme, LearningCurve learningCurve) {
    final trendInfo = _getTrendInfo(learningCurve.trend);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(AppConfig.paddingM),
              decoration: BoxDecoration(
                color: trendInfo['color'].withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                trendInfo['icon'],
                color: trendInfo['color'],
                size: 32,
              ),
            ),
            const SizedBox(width: AppConfig.paddingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'اتجاه التعلم',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConfig.paddingXS),
                  Text(
                    trendInfo['title'],
                    style: theme.textTheme.headlineSmall?.copyWith(
                      color: trendInfo['color'],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    trendInfo['description'],
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChart(BuildContext context, ThemeData theme, LearningCurve learningCurve) {
    final filteredPoints = _getFilteredDataPoints(learningCurve.dataPoints);
    
    if (filteredPoints.isEmpty) {
      return Card(
        child: Container(
          height: 300,
          padding: const EdgeInsets.all(AppConfig.paddingL),
          child: const Center(
            child: Text('لا توجد بيانات للفترة المحددة'),
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'منحنى ${_getMetricTitle(_selectedMetric)}',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.paddingM),
            SizedBox(
              height: 300,
              child: LineChart(
                _buildLineChartData(filteredPoints, theme),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImportantEvents(BuildContext context, ThemeData theme, LearningCurve learningCurve) {
    final importantEvents = learningCurve.dataPoints
        .where((point) => point.isImportantEvent)
        .toList();
    
    if (importantEvents.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الأحداث المهمة',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.paddingM),
            ...importantEvents.map((event) => _buildEventTile(context, theme, event)),
          ],
        ),
      ),
    );
  }

  Widget _buildEventTile(BuildContext context, ThemeData theme, LearningPoint event) {
    final isPositive = event.score >= 0.9;
    final color = isPositive ? Colors.green : Colors.red;
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.paddingS),
      padding: const EdgeInsets.all(AppConfig.paddingM),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConfig.radiusS),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            isPositive ? Icons.celebration : Icons.warning,
            color: color,
          ),
          const SizedBox(width: AppConfig.paddingS),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  event.eventDescription ?? (isPositive ? 'إنجاز ممتاز!' : 'يحتاج مراجعة'),
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  '${event.topicName} - ${_formatDate(event.timestamp)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${(event.score * 100).toInt()}%',
            style: theme.textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInsights(BuildContext context, ThemeData theme, LearningCurve learningCurve) {
    final insights = _generateInsights(learningCurve);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'رؤى وتوصيات',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.paddingM),
            ...insights.map((insight) => Padding(
              padding: const EdgeInsets.only(bottom: AppConfig.paddingS),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.lightbulb,
                    color: Colors.amber,
                    size: 20,
                  ),
                  const SizedBox(width: AppConfig.paddingS),
                  Expanded(
                    child: Text(
                      insight,
                      style: theme.textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.show_chart,
            size: 80,
            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.5),
          ),
          const SizedBox(height: AppConfig.paddingM),
          Text(
            'لا توجد بيانات كافية',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppConfig.paddingS),
          Text(
            'قم بإجراء المزيد من الاختبارات لرؤية منحنيات التعلم',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  LineChartData _buildLineChartData(List<LearningPoint> points, ThemeData theme) {
    final spots = points.asMap().entries.map((entry) {
      final index = entry.key.toDouble();
      final point = entry.value;
      
      double value;
      switch (_selectedMetric) {
        case 'accuracy':
          value = point.accuracy * 100;
          break;
        case 'speed':
          value = point.speed;
          break;
        default:
          value = point.score * 100;
      }
      
      return FlSpot(index, value);
    }).toList();

    return LineChartData(
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        horizontalInterval: 20,
        verticalInterval: 1,
        getDrawingHorizontalLine: (value) {
          return FlLine(
            color: theme.colorScheme.outline.withOpacity(0.2),
            strokeWidth: 1,
          );
        },
        getDrawingVerticalLine: (value) {
          return FlLine(
            color: theme.colorScheme.outline.withOpacity(0.2),
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: 1,
            getTitlesWidget: (value, meta) {
              final index = value.toInt();
              if (index >= 0 && index < points.length) {
                return SideTitleWidget(
                  axisSide: meta.axisSide,
                  child: Text(
                    _formatDateShort(points[index].timestamp),
                    style: const TextStyle(fontSize: 10),
                  ),
                );
              }
              return const Text('');
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: 20,
            getTitlesWidget: (value, meta) {
              return Text(
                value.toInt().toString(),
                style: const TextStyle(fontSize: 10),
              );
            },
            reservedSize: 42,
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
      ),
      minX: 0,
      maxX: spots.length.toDouble() - 1,
      minY: 0,
      maxY: 100,
      lineBarsData: [
        LineChartBarData(
          spots: spots,
          isCurved: true,
          gradient: LinearGradient(
            colors: [
              _getMetricColor(_selectedMetric),
              _getMetricColor(_selectedMetric).withOpacity(0.3),
            ],
          ),
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: FlDotData(
            show: true,
            getDotPainter: (spot, percent, barData, index) {
              final point = points[index];
              return FlDotCirclePainter(
                radius: point.isImportantEvent ? 6 : 4,
                color: point.isImportantEvent ? Colors.red : _getMetricColor(_selectedMetric),
                strokeWidth: point.isImportantEvent ? 2 : 0,
                strokeColor: Colors.white,
              );
            },
          ),
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              colors: [
                _getMetricColor(_selectedMetric).withOpacity(0.3),
                _getMetricColor(_selectedMetric).withOpacity(0.0),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ],
    );
  }

  List<LearningPoint> _getFilteredDataPoints(List<LearningPoint> points) {
    final days = int.parse(_selectedPeriod);
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    
    return points.where((point) => point.timestamp.isAfter(cutoffDate)).toList();
  }

  Map<String, dynamic> _getTrendInfo(LearningTrend trend) {
    switch (trend) {
      case LearningTrend.improving:
        return {
          'title': 'تحسن مستمر',
          'description': 'أداؤك يتحسن بشكل مستمر، استمر!',
          'icon': Icons.trending_up,
          'color': Colors.green,
        };
      case LearningTrend.declining:
        return {
          'title': 'تراجع في الأداء',
          'description': 'يحتاج لمزيد من التركيز والممارسة',
          'icon': Icons.trending_down,
          'color': Colors.red,
        };
      case LearningTrend.fluctuating:
        return {
          'title': 'أداء متذبذب',
          'description': 'حاول الحفاظ على نمط ثابت في الدراسة',
          'icon': Icons.show_chart,
          'color': Colors.orange,
        };
      default:
        return {
          'title': 'أداء مستقر',
          'description': 'أداؤك ثابت، يمكن تحسينه أكثر',
          'icon': Icons.horizontal_rule,
          'color': Colors.blue,
        };
    }
  }

  String _getMetricTitle(String metric) {
    switch (metric) {
      case 'accuracy':
        return 'الدقة';
      case 'speed':
        return 'السرعة';
      default:
        return 'النقاط';
    }
  }

  Color _getMetricColor(String metric) {
    switch (metric) {
      case 'accuracy':
        return Colors.green;
      case 'speed':
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateShort(DateTime date) {
    return '${date.day}/${date.month}';
  }

  List<String> _generateInsights(LearningCurve learningCurve) {
    final insights = <String>[];
    final points = learningCurve.dataPoints;
    
    if (points.isEmpty) return insights;
    
    // Calculate average performance
    final avgScore = points.map((p) => p.score).reduce((a, b) => a + b) / points.length;
    
    if (avgScore > 0.8) {
      insights.add('أداؤك ممتاز! متوسط نقاطك ${(avgScore * 100).toInt()}%');
    } else if (avgScore > 0.6) {
      insights.add('أداؤك جيد، يمكن تحسينه أكثر. متوسط نقاطك ${(avgScore * 100).toInt()}%');
    } else {
      insights.add('يحتاج أداؤك لمزيد من التحسين. متوسط نقاطك ${(avgScore * 100).toInt()}%');
    }
    
    // Check consistency
    final recentPoints = points.length > 5 ? points.sublist(points.length - 5) : points;
    if (recentPoints.length >= 3) {
      final variance = _calculateVariance(recentPoints.map((p) => p.score).toList());
      if (variance < 0.05) {
        insights.add('أداؤك مستقر ومتسق في الاختبارات الأخيرة');
      } else {
        insights.add('حاول الحفاظ على نمط ثابت في الدراسة لتحسين الاستقرار');
      }
    }
    
    // Speed insights
    final avgSpeed = points.map((p) => p.speed).reduce((a, b) => a + b) / points.length;
    if (avgSpeed > 2.0) {
      insights.add('سرعتك في الإجابة جيدة (${avgSpeed.toStringAsFixed(1)} سؤال/دقيقة)');
    } else {
      insights.add('يمكن تحسين سرعة الإجابة مع الممارسة');
    }
    
    return insights;
  }

  double _calculateVariance(List<double> values) {
    final mean = values.reduce((a, b) => a + b) / values.length;
    final variance = values.map((v) => (v - mean) * (v - mean)).reduce((a, b) => a + b) / values.length;
    return variance;
  }

  Future<void> _refreshData() async {
    setState(() {
      _isLoading = true;
    });
    await _analyticsService.refreshAnalytics();
    setState(() {
      _isLoading = false;
    });
  }
}
