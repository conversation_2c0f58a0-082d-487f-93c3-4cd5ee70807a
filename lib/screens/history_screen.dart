import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../config/app_config.dart';
import '../services/quiz_service.dart';
import '../models/quiz_model.dart';
import '../models/quiz_result_model.dart';
import '../flutter_gen/gen_l10n/app_localizations.dart';

// Provider for all quizzes
final allQuizzesProvider = FutureProvider<List<Quiz>>((ref) async {
  final quizService = QuizService();
  return await quizService.getAllQuizzes();
});

// Provider for all quiz results
final allResultsProvider = FutureProvider<List<QuizResult>>((ref) async {
  final quizService = QuizService();
  return await quizService.getAllQuizResults();
});

// Provider for selected tab
final selectedTabProvider = StateProvider<int>((ref) => 0);

class HistoryScreen extends ConsumerWidget {
  const HistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final selectedTab = ref.watch(selectedTabProvider);

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: Text(l10n.history),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.pop(),
          ),
          bottom: TabBar(
            onTap: (index) => ref.read(selectedTabProvider.notifier).state = index,
            tabs: [
              Tab(
                icon: const Icon(Icons.quiz),
                text: l10n.quizzes,
              ),
              Tab(
                icon: const Icon(Icons.assessment),
                text: l10n.results,
              ),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            _buildQuizzesTab(context, l10n, theme, ref),
            _buildResultsTab(context, l10n, theme, ref),
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () => context.push('/upload'),
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildQuizzesTab(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    WidgetRef ref,
  ) {
    final quizzesAsync = ref.watch(allQuizzesProvider);

    return quizzesAsync.when(
      data: (quizzes) {
        if (quizzes.isEmpty) {
          return _buildEmptyState(
            context,
            l10n,
            theme,
            Icons.quiz_outlined,
            l10n.noQuizzesYet,
            l10n.createFirstQuiz,
            () => context.push('/upload'),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(allQuizzesProvider);
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConfig.paddingM),
            itemCount: quizzes.length,
            itemBuilder: (context, index) {
              final quiz = quizzes[index];
              return _buildQuizCard(context, l10n, theme, quiz, ref);
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorState(context, l10n, theme, error.toString()),
    );
  }

  Widget _buildResultsTab(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    WidgetRef ref,
  ) {
    final resultsAsync = ref.watch(allResultsProvider);

    return resultsAsync.when(
      data: (results) {
        if (results.isEmpty) {
          return _buildEmptyState(
            context,
            l10n,
            theme,
            Icons.assessment_outlined,
            l10n.noQuizResults,
            'Complete a quiz to see results here',
            () => context.push('/upload'),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(allResultsProvider);
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConfig.paddingM),
            itemCount: results.length,
            itemBuilder: (context, index) {
              final result = results[index];
              return _buildResultCard(context, l10n, theme, result);
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorState(context, l10n, theme, error.toString()),
    );
  }

  Widget _buildQuizCard(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    Quiz quiz,
    WidgetRef ref,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConfig.paddingM),
      child: InkWell(
        onTap: () => context.push('/quiz/${quiz.id}'),
        borderRadius: BorderRadius.circular(AppConfig.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppConfig.paddingS),
                    decoration: BoxDecoration(
                      color: _getStatusColor(quiz.status),
                      borderRadius: BorderRadius.circular(AppConfig.radiusS),
                    ),
                    child: Icon(
                      _getStatusIcon(quiz.status),
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: AppConfig.paddingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          quiz.title,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          quiz.status.name.toUpperCase(),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: _getStatusColor(quiz.status),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleQuizAction(context, value, quiz, ref),
                    itemBuilder: (context) => [
                      if (quiz.status == QuizStatus.ready)
                        PopupMenuItem(
                          value: 'start',
                          child: Row(
                            children: [
                              const Icon(Icons.play_arrow),
                              const SizedBox(width: AppConfig.paddingS),
                              Text(l10n.startQuiz),
                            ],
                          ),
                        ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            const Icon(Icons.delete, color: AppConfig.errorColor),
                            const SizedBox(width: AppConfig.paddingS),
                            Text(l10n.delete),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: AppConfig.paddingM),
              
              if (quiz.description.isNotEmpty) ...[
                Text(
                  quiz.description,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: AppConfig.paddingM),
              ],
              
              Row(
                children: [
                  _buildInfoChip(
                    context,
                    '${quiz.questions.length} questions',
                    Icons.quiz,
                  ),
                  const SizedBox(width: AppConfig.paddingS),
                  if (quiz.timeLimit > 0)
                    _buildInfoChip(
                      context,
                      '${quiz.timeLimit} min',
                      Icons.timer,
                    ),
                  const Spacer(),
                  Text(
                    _formatDate(quiz.createdAt),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildResultCard(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    QuizResult result,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConfig.paddingM),
      child: InkWell(
        onTap: () => context.push('/results/${result.id}'),
        borderRadius: BorderRadius.circular(AppConfig.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.paddingM),
          child: Row(
            children: [
              // Score Circle
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _getScoreColor(result.scorePercentage),
                ),
                child: Center(
                  child: Text(
                    '${result.scorePercentage.toInt()}%',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppConfig.paddingM),
              
              // Result Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      result.quizTitle,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConfig.paddingXS),
                    Text(
                      '${result.correctAnswers}/${result.totalQuestions} correct • ${result.grade}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: AppConfig.paddingXS),
                    Text(
                      result.completedAt != null 
                          ? _formatDate(result.completedAt!)
                          : 'In progress',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Performance Badge
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConfig.paddingS,
                  vertical: AppConfig.paddingXS,
                ),
                decoration: BoxDecoration(
                  color: _getScoreColor(result.scorePercentage).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppConfig.radiusS),
                ),
                child: Text(
                  result.performanceLevel,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: _getScoreColor(result.scorePercentage),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(BuildContext context, String label, IconData icon) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConfig.paddingS,
        vertical: AppConfig.paddingXS,
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(AppConfig.radiusS),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: AppConfig.paddingXS),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onAction,
  ) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 80,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: AppConfig.paddingL),
            Text(
              title,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConfig.paddingS),
            Text(
              subtitle,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConfig.paddingL),
            ElevatedButton(
              onPressed: onAction,
              child: Text(l10n.createQuiz),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    String error,
  ) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: AppConfig.paddingL),
            Text(
              l10n.error,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: AppConfig.paddingS),
            Text(
              error,
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConfig.paddingL),
            ElevatedButton(
              onPressed: () => context.pop(),
              child: Text(l10n.close),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(QuizStatus status) {
    switch (status) {
      case QuizStatus.draft:
        return AppConfig.warningColor;
      case QuizStatus.ready:
        return AppConfig.successColor;
      case QuizStatus.inProgress:
        return AppConfig.primaryColor;
      case QuizStatus.completed:
        return AppConfig.successColor;
    }
  }

  IconData _getStatusIcon(QuizStatus status) {
    switch (status) {
      case QuizStatus.draft:
        return Icons.edit;
      case QuizStatus.ready:
        return Icons.play_arrow;
      case QuizStatus.inProgress:
        return Icons.hourglass_empty;
      case QuizStatus.completed:
        return Icons.check_circle;
    }
  }

  Color _getScoreColor(double score) {
    if (score >= 85) return AppConfig.successColor;
    if (score >= 70) return AppConfig.warningColor;
    return AppConfig.errorColor;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _handleQuizAction(BuildContext context, String action, Quiz quiz, WidgetRef ref) {
    switch (action) {
      case 'start':
        context.push('/quiz/${quiz.id}');
        break;
      case 'delete':
        _showDeleteDialog(context, quiz, ref);
        break;
    }
  }

  void _showDeleteDialog(BuildContext context, Quiz quiz, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Quiz'),
        content: Text('Are you sure you want to delete "${quiz.title}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              try {
                await QuizService().deleteQuiz(quiz.id);
                ref.invalidate(allQuizzesProvider);
                if (context.mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Quiz deleted successfully')),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Failed to delete quiz: $e')),
                  );
                }
              }
            },
            child: const Text('Delete', style: TextStyle(color: AppConfig.errorColor)),
          ),
        ],
      ),
    );
  }
}
