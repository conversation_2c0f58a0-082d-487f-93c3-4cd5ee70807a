import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/app_localizations.dart';

class LanguageNotifier extends StateNotifier<Locale> {
  LanguageNotifier() : super(const Locale('ar')) {
    _loadLanguage();
  }

  static const String _languageKey = 'selected_language';

  Future<void> _loadLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final languageCode = prefs.getString(_languageKey) ?? 'ar';
      state = Locale(languageCode);
    } catch (e) {
      // If loading fails, keep default Arabic
      state = const Locale('ar');
    }
  }

  Future<void> setLanguage(AppLanguage language) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, language.code);
      state = Locale(language.code);
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> setLocale(Locale locale) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, locale.languageCode);
      state = locale;
    } catch (e) {
      // Handle error silently
    }
  }

  AppLanguage get currentLanguage {
    return state.languageCode == 'en' ? AppLanguage.english : AppLanguage.arabic;
  }

  bool get isArabic => state.languageCode == 'ar';
  bool get isEnglish => state.languageCode == 'en';

  TextDirection get textDirection {
    return isArabic ? TextDirection.rtl : TextDirection.ltr;
  }
}

final languageProvider = StateNotifierProvider<LanguageNotifier, Locale>((ref) {
  return LanguageNotifier();
});

// Helper provider for text direction
final textDirectionProvider = Provider<TextDirection>((ref) {
  final locale = ref.watch(languageProvider);
  return locale.languageCode == 'ar' ? TextDirection.rtl : TextDirection.ltr;
});

// Helper provider for checking if current language is Arabic
final isArabicProvider = Provider<bool>((ref) {
  final locale = ref.watch(languageProvider);
  return locale.languageCode == 'ar';
});

// Helper provider for checking if current language is English
final isEnglishProvider = Provider<bool>((ref) {
  final locale = ref.watch(languageProvider);
  return locale.languageCode == 'en';
});

// Helper provider for getting current AppLanguage
final currentLanguageProvider = Provider<AppLanguage>((ref) {
  final locale = ref.watch(languageProvider);
  return locale.languageCode == 'en' ? AppLanguage.english : AppLanguage.arabic;
});
