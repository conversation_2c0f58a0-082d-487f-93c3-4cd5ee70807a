import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:go_router/go_router.dart';
import 'config/app_config.dart';
import 'screens/home_screen.dart';
import 'screens/pdf_upload_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/simple_screens.dart';
import 'screens/quiz_screen_real.dart';
import 'screens/results_screen_real.dart';
import 'screens/history_screen_real.dart' as history_real;

// Providers for app state management
final themeProvider = StateProvider<ThemeMode>((ref) => AppConfig.defaultThemeMode);
final localeProvider = StateProvider<Locale>((ref) => AppConfig.defaultLocale);

class QuePdfApp extends ConsumerWidget {
  const QuePdfApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeProvider);
    final locale = ref.watch(localeProvider);

    return MaterialApp.router(
      title: AppConfig.appName,
      debugShowCheckedModeBanner: false,
      
      // Localization
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: AppConfig.supportedLocales,
      locale: locale,
      
      // Theme
      theme: _buildLightTheme(),
      darkTheme: _buildDarkTheme(),
      themeMode: themeMode,
      
      // Routing
      routerConfig: _router,
    );
  }

  // Light theme configuration
  ThemeData _buildLightTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppConfig.primaryColor,
        brightness: Brightness.light,
      ),
      // fontFamily: 'Roboto',
      
      // AppBar theme
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: 0,
        scrolledUnderElevation: 1,
      ),
      
      // Card theme
      cardTheme: CardTheme(
        elevation: AppConfig.elevationS,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConfig.radiusM),
        ),
      ),
      
      // Elevated button theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: AppConfig.elevationS,
          padding: const EdgeInsets.symmetric(
            horizontal: AppConfig.paddingL,
            vertical: AppConfig.paddingM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.radiusM),
          ),
        ),
      ),
      
      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConfig.radiusM),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConfig.paddingM,
          vertical: AppConfig.paddingM,
        ),
      ),
      
      // Bottom navigation bar theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        type: BottomNavigationBarType.fixed,
        elevation: AppConfig.elevationM,
      ),
    );
  }

  // Dark theme configuration
  ThemeData _buildDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppConfig.primaryColor,
        brightness: Brightness.dark,
      ),
      // fontFamily: 'Roboto',
      
      // AppBar theme
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: 0,
        scrolledUnderElevation: 1,
      ),
      
      // Card theme
      cardTheme: CardTheme(
        elevation: AppConfig.elevationS,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConfig.radiusM),
        ),
      ),
      
      // Elevated button theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: AppConfig.elevationS,
          padding: const EdgeInsets.symmetric(
            horizontal: AppConfig.paddingL,
            vertical: AppConfig.paddingM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.radiusM),
          ),
        ),
      ),
      
      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConfig.radiusM),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConfig.paddingM,
          vertical: AppConfig.paddingM,
        ),
      ),
      
      // Bottom navigation bar theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        type: BottomNavigationBarType.fixed,
        elevation: AppConfig.elevationM,
      ),
    );
  }

  // Router configuration
  static final GoRouter _router = GoRouter(
    initialLocation: '/',
    routes: [
      GoRoute(
        path: '/',
        name: 'home',
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: '/upload',
        name: 'upload',
        builder: (context, state) => const PdfUploadScreen(),
      ),
      GoRoute(
        path: '/generate/:pdfPath',
        name: 'generate',
        builder: (context, state) {
          final pdfPath = state.pathParameters['pdfPath']!;
          final title = state.uri.queryParameters['title'] ?? '';
          final description = state.uri.queryParameters['description'] ?? '';
          final questionCount = int.tryParse(
            state.uri.queryParameters['questionCount'] ?? '10'
          ) ?? 10;
          
          return QuizGenerationScreen(
            pdfPath: pdfPath,
            title: title,
            description: description,
            questionCount: questionCount,
          );
        },
      ),
      GoRoute(
        path: '/quiz/:quizId',
        name: 'quiz',
        builder: (context, state) {
          final quizId = state.pathParameters['quizId']!;
          return QuizScreen(quizId: quizId);
        },
      ),
      GoRoute(
        path: '/results/:resultId',
        name: 'results',
        builder: (context, state) {
          final resultId = state.pathParameters['resultId']!;
          return ResultsScreen(resultId: resultId);
        },
      ),
      GoRoute(
        path: '/history',
        name: 'history',
        builder: (context, state) => const history_real.HistoryScreen(),
      ),
      GoRoute(
        path: '/settings',
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('Error'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppConfig.errorColor,
            ),
            const SizedBox(height: AppConfig.paddingM),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: AppConfig.paddingS),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConfig.paddingL),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
}
