import 'package:flutter/material.dart';

class AppConfig {
  // App Information
  static const String appName = 'QuePDF';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Professional PDF to Quiz converter with AI-powered question generation';

  // API Configuration
  static const String geminiApiBaseUrl = 'https://generativelanguage.googleapis.com';
  static const String geminiModel = 'gemini-1.5-flash';
  
  // Quiz Configuration
  static const int defaultQuizTimeLimit = 30; // minutes
  static const int maxQuestionsPerQuiz = 50;
  static const int minQuestionsPerQuiz = 5;
  static const int defaultQuestionsCount = 10;
  
  // PDF Configuration
  static const int maxPdfSizeMB = 50;
  static const List<String> supportedPdfExtensions = ['.pdf'];
  
  // Storage Configuration
  static const String databaseName = 'quepdf.db';
  static const int databaseVersion = 1;
  
  // UI Configuration
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration splashDuration = Duration(seconds: 2);
  
  // Colors
  static const Color primaryColor = Color(0xFF6366F1);
  static const Color secondaryColor = Color(0xFF8B5CF6);
  static const Color accentColor = Color(0xFF06B6D4);
  static const Color successColor = Color(0xFF10B981);
  static const Color warningColor = Color(0xFFF59E0B);
  static const Color errorColor = Color(0xFFEF4444);
  static const Color backgroundColor = Color(0xFFF8FAFC);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color onSurfaceColor = Color(0xFF1E293B);
  
  // Text Styles
  static const TextStyle headingStyle = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: onSurfaceColor,
  );
  
  static const TextStyle subheadingStyle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: onSurfaceColor,
  );
  
  static const TextStyle bodyStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: onSurfaceColor,
  );
  
  static const TextStyle captionStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: Color(0xFF64748B),
  );
  
  // Spacing
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  
  // Border Radius
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 24.0;
  
  // Elevation
  static const double elevationS = 2.0;
  static const double elevationM = 4.0;
  static const double elevationL = 8.0;
  
  // Breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1024;
  static const double desktopBreakpoint = 1440;
  
  // Feature Flags
  static const bool enableOfflineMode = true;
  static const bool enableAnalytics = true;
  static const bool enableDarkMode = true;
  static const bool enableMultiLanguage = true;
  
  // Supported Languages
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English
    Locale('ar', 'SA'), // Arabic
  ];
  
  // Default Settings
  static const Locale defaultLocale = Locale('en', 'US');
  static const ThemeMode defaultThemeMode = ThemeMode.system;
  
  // Quiz Generation Prompts
  static const String quizGenerationPrompt = '''
Generate a quiz with multiple-choice questions based on the following PDF content.
Please follow these guidelines:
1. Create exactly {questionCount} questions
2. Each question should have 4 options (A, B, C, D)
3. Indicate the correct answer
4. Provide a brief explanation for each answer
5. Vary the difficulty levels (easy, medium, hard)
6. Focus on key concepts and important information
7. Ensure questions are clear and unambiguous
8. Return the response in JSON format

Content:
{pdfContent}

Please return a JSON object with this structure:
{
  "questions": [
    {
      "text": "Question text here",
      "options": ["Option A", "Option B", "Option C", "Option D"],
      "correctAnswerIndex": 0,
      "explanation": "Explanation for the correct answer",
      "difficulty": "easy|medium|hard",
      "category": "optional category"
    }
  ]
}
''';

  // Error Messages
  static const String genericErrorMessage = 'An unexpected error occurred. Please try again.';
  static const String networkErrorMessage = 'Network error. Please check your internet connection.';
  static const String pdfParsingErrorMessage = 'Failed to parse PDF file. Please ensure it\'s a valid PDF.';
  static const String quizGenerationErrorMessage = 'Failed to generate quiz. Please try again.';
  static const String storageErrorMessage = 'Storage error. Please check available space.';
  
  // Success Messages
  static const String quizGeneratedSuccessMessage = 'Quiz generated successfully!';
  static const String quizCompletedSuccessMessage = 'Quiz completed successfully!';
  static const String pdfUploadedSuccessMessage = 'PDF uploaded successfully!';
}

// Environment-specific configurations
class EnvironmentConfig {
  static const String _environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
  
  static bool get isDevelopment => _environment == 'development';
  static bool get isProduction => _environment == 'production';
  static bool get isTesting => _environment == 'testing';
  
  static String get geminiApiKey {
    // In production, this should be loaded from secure storage or environment variables
    return const String.fromEnvironment('GEMINI_API_KEY', defaultValue: 'AIzaSyA0C1cNjFlLfKUB0ovHS84fyucjODjsvQI');
  }
  
  static bool get enableLogging => isDevelopment || isTesting;
  static bool get enableDebugMode => isDevelopment;
}
