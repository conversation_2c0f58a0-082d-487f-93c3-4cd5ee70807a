import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/language_provider.dart';
import '../utils/app_localizations.dart';

class SimpleLanguageToggle extends ConsumerWidget {
  const SimpleLanguageToggle({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLanguage = ref.watch(currentLanguageProvider);
    final languageNotifier = ref.read(languageProvider.notifier);

    return PopupMenuButton<AppLanguage>(
      icon: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.language,
              size: 16,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 4),
            Text(
              currentLanguage.code.toUpperCase(),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
      onSelected: (AppLanguage language) {
        languageNotifier.setLanguage(language);
      },
      itemBuilder: (BuildContext context) => AppLanguage.values.map((language) {
        final isSelected = currentLanguage == language;
        return PopupMenuItem<AppLanguage>(
          value: language,
          child: Row(
            children: [
              Icon(
                isSelected ? Icons.check : Icons.language,
                size: 20,
                color: isSelected ? Theme.of(context).colorScheme.primary : null,
              ),
              const SizedBox(width: 8),
              Text(
                language.name,
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? Theme.of(context).colorScheme.primary : null,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
