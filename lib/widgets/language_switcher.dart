import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/language_provider.dart';
import '../utils/app_localizations.dart';

class LanguageSwitcher extends ConsumerWidget {
  final bool showLabel;
  final bool isCompact;

  const LanguageSwitcher({
    super.key,
    this.showLabel = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLanguage = ref.watch(currentLanguageProvider);
    final languageNotifier = ref.read(languageProvider.notifier);
    final localizations = AppLocalizations.of(context);

    if (isCompact) {
      return PopupMenuButton<AppLanguage>(
        icon: Icon(
          Icons.language,
          color: Theme.of(context).colorScheme.onSurface,
        ),
        onSelected: (AppLanguage language) {
          languageNotifier.setLanguage(language);
        },
        itemBuilder: (BuildContext context) => AppLanguage.values.map((language) {
          return PopupMenuItem<AppLanguage>(
            value: language,
            child: Row(
              children: [
                Icon(
                  currentLanguage == language ? Icons.check : Icons.language,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(language.name),
              ],
            ),
          );
        }).toList(),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (showLabel) ...[
              Row(
                children: [
                  Icon(
                    Icons.language,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    localizations.language,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],
            Row(
              children: AppLanguage.values.map((language) {
                final isSelected = currentLanguage == language;
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: _buildLanguageButton(
                      context,
                      language,
                      isSelected,
                      () => languageNotifier.setLanguage(language),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageButton(
    BuildContext context,
    AppLanguage language,
    bool isSelected,
    VoidCallback onTap,
  ) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primary
              : theme.colorScheme.surfaceVariant,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.outline.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getLanguageIcon(language),
              color: isSelected
                  ? theme.colorScheme.onPrimary
                  : theme.colorScheme.onSurfaceVariant,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              language.name,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected
                    ? theme.colorScheme.onPrimary
                    : theme.colorScheme.onSurfaceVariant,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getLanguageIcon(AppLanguage language) {
    switch (language) {
      case AppLanguage.arabic:
        return Icons.translate;
      case AppLanguage.english:
        return Icons.language;
    }
  }
}

class LanguageDropdown extends ConsumerWidget {
  final String? title;
  final bool enabled;

  const LanguageDropdown({
    super.key,
    this.title,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLanguage = ref.watch(currentLanguageProvider);
    final languageNotifier = ref.read(languageProvider.notifier);
    final localizations = AppLocalizations.of(context);

    return ListTile(
      leading: const Icon(Icons.language),
      title: Text(title ?? localizations.language),
      subtitle: Text(currentLanguage.name),
      trailing: DropdownButton<AppLanguage>(
        value: currentLanguage,
        underline: const SizedBox.shrink(),
        items: AppLanguage.values.map((language) {
          return DropdownMenuItem<AppLanguage>(
            value: language,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _getLanguageIcon(language),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(language.name),
              ],
            ),
          );
        }).toList(),
        onChanged: enabled
            ? (AppLanguage? language) {
                if (language != null) {
                  languageNotifier.setLanguage(language);
                }
              }
            : null,
      ),
    );
  }

  IconData _getLanguageIcon(AppLanguage language) {
    switch (language) {
      case AppLanguage.arabic:
        return Icons.translate;
      case AppLanguage.english:
        return Icons.language;
    }
  }
}

class LanguageToggle extends ConsumerWidget {
  final bool showLabels;

  const LanguageToggle({
    super.key,
    this.showLabels = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLanguage = ref.watch(currentLanguageProvider);
    final languageNotifier = ref.read(languageProvider.notifier);
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: AppLanguage.values.map((language) {
          final isSelected = currentLanguage == language;
          return GestureDetector(
            onTap: () => languageNotifier.setLanguage(language),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              margin: const EdgeInsets.all(2),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected ? theme.colorScheme.primary : Colors.transparent,
                borderRadius: BorderRadius.circular(23),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _getLanguageIcon(language),
                    color: isSelected
                        ? theme.colorScheme.onPrimary
                        : theme.colorScheme.onSurfaceVariant,
                    size: 18,
                  ),
                  if (showLabels) ...[
                    const SizedBox(width: 6),
                    Text(
                      language.code.toUpperCase(),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isSelected
                            ? theme.colorScheme.onPrimary
                            : theme.colorScheme.onSurfaceVariant,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  IconData _getLanguageIcon(AppLanguage language) {
    switch (language) {
      case AppLanguage.arabic:
        return Icons.translate;
      case AppLanguage.english:
        return Icons.language;
    }
  }
}
