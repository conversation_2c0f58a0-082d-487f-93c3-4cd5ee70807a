# quepdf

A new Flutter project.

## Getting Started

# QuePDF - Professional PDF to Quiz Converter

A professional Android application built with Flutter that converts PDF files into interactive quizzes using AI-powered question generation with Google's Gemini API.

## 🚀 Features

### Core Functionality
- **PDF Processing**: Extract text content from PDF files with validation
- **AI Quiz Generation**: Generate multiple-choice questions using Google Gemini API
- **Interactive Quiz Interface**: Clean, user-friendly quiz-taking experience
- **Automatic Grading**: AI-powered grading with detailed feedback
- **Progress Tracking**: Track quiz results and performance over time

### User Experience
- **Bilingual Support**: Full Arabic (RTL) and English language support
- **Professional UI/UX**: Modern Material Design 3 interface
- **Responsive Design**: Optimized for different screen sizes
- **Dark/Light Theme**: System-aware theme switching
- **Offline Capability**: Take generated quizzes without internet connection

### Technical Features
- **Local Storage**: SQLite database for quiz and result persistence
- **File Management**: Secure PDF file handling with validation
- **Performance Optimization**: Efficient PDF processing and memory management
- **Error Handling**: Comprehensive error handling and user feedback
- **Security**: Secure API key management and data protection

## 📱 Screenshots

*Screenshots will be added here once the app is built and tested*

## 🛠️ Technical Stack

- **Framework**: Flutter 3.7.2+
- **State Management**: Riverpod
- **Navigation**: GoRouter
- **Database**: SQLite (sqflite)
- **PDF Processing**: Syncfusion Flutter PDF
- **AI Integration**: Google Generative AI (Gemini)
- **Localization**: Flutter Intl
- **UI Components**: Material Design 3

## 📋 Prerequisites

Before running this application, ensure you have:

1. **Flutter SDK** (3.7.2 or higher)
2. **Android Studio** or **VS Code** with Flutter extensions
3. **Android SDK** (API level 21 or higher)
4. **Google Gemini API Key** (required for quiz generation)

## 🔧 Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd quepdf
```

### 2. Install Dependencies
```bash
flutter pub get
```

### 3. Configure API Key
Create a `.env` file in the root directory and add your Gemini API key:
```
GEMINI_API_KEY=your_api_key_here
```

Or set it as an environment variable:
```bash
export GEMINI_API_KEY=your_api_key_here
```

### 4. Generate Localization Files
```bash
flutter gen-l10n
```

### 5. Run the Application
```bash
flutter run
```

## 🔑 API Configuration

### Getting a Gemini API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Create a new API key
4. Copy the key and add it to your environment configuration

### API Usage
The app uses the Gemini API for:
- Generating quiz questions from PDF content
- Creating explanations for answers
- Providing personalized feedback on quiz results

## 📁 Project Structure

```
lib/
├── config/
│   └── app_config.dart          # App configuration and constants
├── models/
│   ├── question_model.dart      # Question data model
│   ├── quiz_model.dart          # Quiz data model
│   └── quiz_result_model.dart   # Quiz result data model
├── services/
│   ├── pdf_service.dart         # PDF processing service
│   ├── gemini_service.dart      # AI integration service
│   ├── storage_service.dart     # Local storage service
│   └── quiz_service.dart        # Quiz management service
├── screens/
│   ├── home_screen.dart         # Main dashboard
│   ├── pdf_upload_screen.dart   # PDF selection and configuration
│   ├── quiz_generation_screen.dart # Quiz creation progress
│   ├── quiz_screen.dart         # Interactive quiz interface
│   ├── results_screen.dart      # Quiz results and feedback
│   ├── history_screen.dart      # Quiz history and management
│   └── settings_screen.dart     # App settings and configuration
├── l10n/                        # Localization files
│   ├── app_en.arb              # English translations
│   └── app_ar.arb              # Arabic translations
├── app.dart                     # Main app configuration
└── main.dart                    # App entry point
```

## 🌐 Localization

The app supports:
- **English** (default)
- **Arabic** with full RTL (Right-to-Left) support

To add a new language:
1. Create a new `.arb` file in `lib/l10n/`
2. Add the locale to `AppConfig.supportedLocales`
3. Run `flutter gen-l10n` to generate the localization files

## 🗄️ Database Schema

### Quizzes Table
- `id`: Unique identifier
- `title`: Quiz title
- `description`: Quiz description
- `sourceFileName`: Original PDF filename
- `sourcePdfPath`: Path to source PDF
- `questions`: JSON array of questions
- `status`: Quiz status (draft, ready, inProgress, completed)
- `createdAt`: Creation timestamp
- `timeLimit`: Time limit in minutes
- `category`: Optional category

### Quiz Results Table
- `id`: Unique identifier
- `quizId`: Reference to quiz
- `questionResults`: JSON array of question results
- `status`: Result status
- `startedAt`: Start timestamp
- `completedAt`: Completion timestamp
- `totalTimeSeconds`: Total time taken
- `scorePercentage`: Score percentage
- `feedback`: AI-generated feedback

## 🔒 Security & Privacy

- **API Keys**: Stored securely using environment variables
- **Local Data**: All quiz data stored locally on device
- **File Access**: Minimal permissions requested for PDF access only
- **No Data Collection**: No personal data sent to external servers except for AI processing

## 🧪 Testing

### Running Tests
```bash
# Run all tests
flutter test

# Run tests with coverage
flutter test --coverage

# Run integration tests
flutter drive --target=test_driver/app.dart
```

### Test Structure
- **Unit Tests**: Service layer and model testing
- **Widget Tests**: UI component testing
- **Integration Tests**: End-to-end workflow testing

## 📦 Building for Production

### Android APK
```bash
flutter build apk --release
```

### Android App Bundle
```bash
flutter build appbundle --release
```

### Build with Environment Variables
```bash
flutter build apk --release --dart-define=GEMINI_API_KEY=your_key_here
```

## 🚀 Deployment

### Google Play Store
1. Build the app bundle: `flutter build appbundle --release`
2. Sign the bundle with your keystore
3. Upload to Google Play Console
4. Complete store listing and publish

### Direct APK Distribution
1. Build APK: `flutter build apk --release`
2. Distribute the APK file from `build/app/outputs/flutter-apk/`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support & Issues

- **Bug Reports**: Create an issue with detailed reproduction steps
- **Feature Requests**: Submit an issue with the enhancement label
- **Questions**: Check existing issues or create a new discussion

## 🙏 Acknowledgments

- **Flutter Team**: For the amazing framework
- **Google**: For the Gemini AI API
- **Syncfusion**: For the PDF processing library
- **Community**: For the open-source packages used

## 📊 Performance Considerations

- **PDF Size Limit**: 50MB maximum file size
- **Question Limit**: 5-50 questions per quiz
- **Memory Management**: Efficient PDF processing with cleanup
- **Database Optimization**: Indexed queries for better performance

## 🔄 Version History

- **v1.0.0**: Initial release with core functionality
  - PDF to quiz conversion
  - AI-powered question generation
  - Bilingual support
  - Local storage and offline capability

---

**Made with ❤️ using Flutter**
